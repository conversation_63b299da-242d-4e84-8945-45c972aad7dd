{"version": 3, "sources": ["../../src/lib/worker.ts"], "sourcesContent": ["import type { ChildProcess } from 'child_process'\nimport { Worker as JestWorker } from 'next/dist/compiled/jest-worker'\nimport { Transform } from 'stream'\n\ntype FarmOptions = ConstructorParameters<typeof JestWorker>[1]\n\nconst RESTARTED = Symbol('restarted')\n\nconst cleanupWorkers = (worker: Jest<PERSON><PERSON><PERSON>) => {\n  for (const curWorker of ((worker as any)._workerPool?._workers || []) as {\n    _child?: ChildProcess\n  }[]) {\n    curWorker._child?.kill('SIGINT')\n  }\n}\n\nexport class Worker {\n  private _worker: JestWorker | undefined\n\n  constructor(\n    workerPath: string,\n    options: FarmOptions & {\n      timeout?: number\n      onActivity?: () => void\n      onActivityAbort?: () => void\n      onRestart?: (method: string, args: any[], attempts: number) => void\n      logger?: Pick<typeof console, 'error' | 'info' | 'warn'>\n      exposedMethods: ReadonlyArray<string>\n      enableWorkerThreads?: boolean\n    }\n  ) {\n    let { timeout, onRestart, logger = console, ...farmOptions } = options\n\n    let restartPromise: Promise<typeof RESTARTED>\n    let resolveRestartPromise: (arg: typeof RESTARTED) => void\n    let activeTasks = 0\n\n    this._worker = undefined\n\n    // ensure we end workers if they weren't before exit\n    process.on('exit', () => {\n      this.close()\n    })\n\n    const createWorker = () => {\n      this._worker = new JestWorker(workerPath, {\n        ...farmOptions,\n        forkOptions: {\n          ...farmOptions.forkOptions,\n          env: {\n            ...((farmOptions.forkOptions?.env || {}) as any),\n            ...process.env,\n            IS_NEXT_WORKER: 'true',\n          } as any,\n        },\n        maxRetries: 0,\n      }) as JestWorker\n      restartPromise = new Promise(\n        (resolve) => (resolveRestartPromise = resolve)\n      )\n\n      /**\n       * Jest Worker has two worker types, ChildProcessWorker (uses child_process) and NodeThreadWorker (uses worker_threads)\n       * Next.js uses ChildProcessWorker by default, but it can be switched to NodeThreadWorker with an experimental flag\n       *\n       * We only want to handle ChildProcessWorker's orphan process issue, so we access the private property \"_child\":\n       * https://github.com/facebook/jest/blob/b38d7d345a81d97d1dc3b68b8458b1837fbf19be/packages/jest-worker/src/workers/ChildProcessWorker.ts\n       *\n       * But this property is not available in NodeThreadWorker, so we need to check if we are using ChildProcessWorker\n       */\n      if (!farmOptions.enableWorkerThreads) {\n        for (const worker of ((this._worker as any)._workerPool?._workers ||\n          []) as {\n          _child?: ChildProcess\n        }[]) {\n          worker._child?.on('exit', (code, signal) => {\n            if ((code || (signal && signal !== 'SIGINT')) && this._worker) {\n              logger.error(\n                `Next.js build worker exited with code: ${code} and signal: ${signal}`\n              )\n\n              // if a child process doesn't exit gracefully, we want to bubble up the exit code to the parent process\n              process.exit(code ?? 1)\n            }\n          })\n\n          // if a child process emits a particular message, we track that as activity\n          // so the parent process can keep track of progress\n          worker._child?.on('message', ([, data]: [number, unknown]) => {\n            if (\n              data &&\n              typeof data === 'object' &&\n              'type' in data &&\n              data.type === 'activity'\n            ) {\n              onActivity()\n            }\n          })\n        }\n      }\n\n      let aborted = false\n      const onActivityAbort = () => {\n        if (!aborted) {\n          options.onActivityAbort?.()\n          aborted = true\n        }\n      }\n\n      // Listen to the worker's stdout and stderr, if there's any thing logged, abort the activity first\n      const abortActivityStreamOnLog = new Transform({\n        transform(_chunk, _encoding, callback) {\n          onActivityAbort()\n          callback()\n        },\n      })\n      // Stop the activity if there's any output from the worker\n      this._worker.getStdout().pipe(abortActivityStreamOnLog)\n      this._worker.getStderr().pipe(abortActivityStreamOnLog)\n\n      // Pipe the worker's stdout and stderr to the parent process\n      this._worker.getStdout().pipe(process.stdout)\n      this._worker.getStderr().pipe(process.stderr)\n    }\n    createWorker()\n\n    const onHanging = () => {\n      const worker = this._worker\n      if (!worker) return\n      const resolve = resolveRestartPromise\n      createWorker()\n      logger.warn(\n        `Sending SIGTERM signal to static worker due to timeout${\n          timeout ? ` of ${timeout / 1000} seconds` : ''\n        }. Subsequent errors may be a result of the worker exiting.`\n      )\n      worker.end().then(() => {\n        resolve(RESTARTED)\n      })\n    }\n\n    let hangingTimer: NodeJS.Timeout | false = false\n\n    const onActivity = () => {\n      if (hangingTimer) clearTimeout(hangingTimer)\n      if (options.onActivity) options.onActivity()\n\n      hangingTimer = activeTasks > 0 && setTimeout(onHanging, timeout)\n    }\n\n    for (const method of farmOptions.exposedMethods) {\n      if (method.startsWith('_')) continue\n      ;(this as any)[method] = timeout\n        ? // eslint-disable-next-line no-loop-func\n          async (...args: any[]) => {\n            activeTasks++\n            try {\n              let attempts = 0\n              for (;;) {\n                onActivity()\n                const result = await Promise.race([\n                  (this._worker as any)[method](...args),\n                  restartPromise,\n                ])\n                if (result !== RESTARTED) return result\n                if (onRestart) onRestart(method, args, ++attempts)\n              }\n            } finally {\n              activeTasks--\n              onActivity()\n            }\n          }\n        : (this._worker as any)[method].bind(this._worker)\n    }\n  }\n\n  end(): ReturnType<JestWorker['end']> {\n    const worker = this._worker\n    if (!worker) {\n      throw new Error('Farm is ended, no more calls can be done to it')\n    }\n    cleanupWorkers(worker)\n    this._worker = undefined\n    return worker.end()\n  }\n\n  /**\n   * Quietly end the worker if it exists\n   */\n  close(): void {\n    if (this._worker) {\n      cleanupWorkers(this._worker)\n      this._worker.end()\n    }\n  }\n}\n"], "names": ["Worker", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "logger", "console", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "process", "on", "close", "createWorker", "JestWorker", "forkOptions", "env", "IS_NEXT_WORKER", "maxRetries", "Promise", "resolve", "enableWorkerThreads", "code", "signal", "error", "exit", "data", "type", "onActivity", "aborted", "onActivityAbort", "abortActivityStreamOnLog", "Transform", "transform", "_chunk", "_encoding", "callback", "getStdout", "pipe", "getStderr", "stdout", "stderr", "onHanging", "warn", "end", "then", "hanging<PERSON><PERSON>r", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;4BAfwB;wBACX;AAI1B,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEO,MAAMT;IAGXU,YACEC,UAAkB,EAClBC,OAQC,CACD;QACA,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAASC,OAAO,EAAE,GAAGC,aAAa,GAAGL;QAE/D,IAAIM;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,oDAAoD;QACpDC,QAAQC,EAAE,CAAC,QAAQ;YACjB,IAAI,CAACC,KAAK;QACZ;QAEA,MAAMC,eAAe;gBAMRT;YALX,IAAI,CAACI,OAAO,GAAG,IAAIM,kBAAU,CAAChB,YAAY;gBACxC,GAAGM,WAAW;gBACdW,aAAa;oBACX,GAAGX,YAAYW,WAAW;oBAC1BC,KAAK;wBACH,GAAKZ,EAAAA,2BAAAA,YAAYW,WAAW,qBAAvBX,yBAAyBY,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGN,QAAQM,GAAG;wBACdC,gBAAgB;oBAClB;gBACF;gBACAC,YAAY;YACd;YACAb,iBAAiB,IAAIc,QACnB,CAACC,UAAad,wBAAwBc;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAAChB,YAAYiB,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM9B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACiB,OAAO,CAASf,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH,gBAWA,2EAA2E;oBAC3E,mDAAmD;oBACnDA;qBAbAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAeoB,EAAE,CAAC,QAAQ,CAACW,MAAMC;wBAC/B,IAAI,AAACD,CAAAA,QAASC,UAAUA,WAAW,QAAQ,KAAM,IAAI,CAACf,OAAO,EAAE;4BAC7DN,OAAOsB,KAAK,CACV,CAAC,uCAAuC,EAAEF,KAAK,aAAa,EAAEC,QAAQ;4BAGxE,uGAAuG;4BACvGb,QAAQe,IAAI,CAACH,QAAQ;wBACvB;oBACF;qBAIA/B,kBAAAA,OAAOI,MAAM,qBAAbJ,gBAAeoB,EAAE,CAAC,WAAW,CAAC,GAAGe,KAAwB;wBACvD,IACEA,QACA,OAAOA,SAAS,YAChB,UAAUA,QACVA,KAAKC,IAAI,KAAK,YACd;4BACAC;wBACF;oBACF;gBACF;YACF;YAEA,IAAIC,UAAU;YACd,MAAMC,kBAAkB;gBACtB,IAAI,CAACD,SAAS;oBACZ9B,QAAQ+B,eAAe,oBAAvB/B,QAAQ+B,eAAe,MAAvB/B;oBACA8B,UAAU;gBACZ;YACF;YAEA,kGAAkG;YAClG,MAAME,2BAA2B,IAAIC,iBAAS,CAAC;gBAC7CC,WAAUC,MAAM,EAAEC,SAAS,EAAEC,QAAQ;oBACnCN;oBACAM;gBACF;YACF;YACA,0DAA0D;YAC1D,IAAI,CAAC5B,OAAO,CAAC6B,SAAS,GAAGC,IAAI,CAACP;YAC9B,IAAI,CAACvB,OAAO,CAAC+B,SAAS,GAAGD,IAAI,CAACP;YAE9B,4DAA4D;YAC5D,IAAI,CAACvB,OAAO,CAAC6B,SAAS,GAAGC,IAAI,CAAC5B,QAAQ8B,MAAM;YAC5C,IAAI,CAAChC,OAAO,CAAC+B,SAAS,GAAGD,IAAI,CAAC5B,QAAQ+B,MAAM;QAC9C;QACA5B;QAEA,MAAM6B,YAAY;YAChB,MAAMnD,SAAS,IAAI,CAACiB,OAAO;YAC3B,IAAI,CAACjB,QAAQ;YACb,MAAM6B,UAAUd;YAChBO;YACAX,OAAOyC,IAAI,CACT,CAAC,sDAAsD,EACrD3C,UAAU,CAAC,IAAI,EAAEA,UAAU,KAAK,QAAQ,CAAC,GAAG,GAC7C,0DAA0D,CAAC;YAE9DT,OAAOqD,GAAG,GAAGC,IAAI,CAAC;gBAChBzB,QAAQhC;YACV;QACF;QAEA,IAAI0D,eAAuC;QAE3C,MAAMlB,aAAa;YACjB,IAAIkB,cAAcC,aAAaD;YAC/B,IAAI/C,QAAQ6B,UAAU,EAAE7B,QAAQ6B,UAAU;YAE1CkB,eAAevC,cAAc,KAAKyC,WAAWN,WAAW1C;QAC1D;QAEA,KAAK,MAAMiD,UAAU7C,YAAY8C,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGjD,UAErB,OAAO,GAAGoD;gBACR7C;gBACA,IAAI;oBACF,IAAI8C,WAAW;oBACf,OAAS;wBACPzB;wBACA,MAAM0B,SAAS,MAAMnC,QAAQoC,IAAI,CAAC;4BAC/B,IAAI,CAAC/C,OAAO,AAAQ,CAACyC,OAAO,IAAIG;4BACjC/C;yBACD;wBACD,IAAIiD,WAAWlE,WAAW,OAAOkE;wBACjC,IAAIrD,WAAWA,UAAUgD,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACR9C;oBACAqB;gBACF;YACF,IACA,AAAC,IAAI,CAACpB,OAAO,AAAQ,CAACyC,OAAO,CAACO,IAAI,CAAC,IAAI,CAAChD,OAAO;QACrD;IACF;IAEAoC,MAAqC;QACnC,MAAMrD,SAAS,IAAI,CAACiB,OAAO;QAC3B,IAAI,CAACjB,QAAQ;YACX,MAAM,qBAA2D,CAA3D,IAAIkE,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QACAnE,eAAeC;QACf,IAAI,CAACiB,OAAO,GAAGC;QACf,OAAOlB,OAAOqD,GAAG;IACnB;IAEA;;GAEC,GACDhC,QAAc;QACZ,IAAI,IAAI,CAACJ,OAAO,EAAE;YAChBlB,eAAe,IAAI,CAACkB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACoC,GAAG;QAClB;IACF;AACF"}