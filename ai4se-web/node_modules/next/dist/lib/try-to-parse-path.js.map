{"version": 3, "sources": ["../../src/lib/try-to-parse-path.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { parse, tokensToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { parse as parseURL } from 'url'\nimport isError from './is-error'\n\ninterface ParseResult {\n  error?: any\n  parsedPath: string\n  regexStr?: string\n  route: string\n  tokens?: Token[]\n}\n\n/**\n * If there is an error show our error link but still show original error or\n * a formatted one if we can\n */\nfunction reportError({ route, parsedPath }: ParseResult, err: any) {\n  let errMatches\n  if (isError(err) && (errMatches = err.message.match(/at (\\d{0,})/))) {\n    const position = parseInt(errMatches[1], 10)\n    console.error(\n      `\\nError parsing \\`${route}\\` ` +\n        `https://nextjs.org/docs/messages/invalid-route-source\\n` +\n        `Reason: ${err.message}\\n\\n` +\n        `  ${parsedPath}\\n` +\n        `  ${new Array(position).fill(' ').join('')}^\\n`\n    )\n  } else {\n    console.error(\n      `\\nError parsing ${route} https://nextjs.org/docs/messages/invalid-route-source`,\n      err\n    )\n  }\n}\n\n/**\n * Attempts to parse a given route with `path-to-regexp` and returns an object\n * with the result. Whenever an error happens on parse, it will print an error\n * attempting to find the error position and showing a link to the docs. When\n * `handleUrl` is set to `true` it will also attempt to parse the route\n * and use the resulting pathname to parse with `path-to-regexp`.\n */\nexport function tryToParsePath(\n  route: string,\n  options?: {\n    handleUrl?: boolean\n  }\n): ParseResult {\n  const result: ParseResult = { route, parsedPath: route }\n  try {\n    if (options?.handleUrl) {\n      const parsed = parseURL(route, true)\n      result.parsedPath = `${parsed.pathname!}${parsed.hash || ''}`\n    }\n\n    result.tokens = parse(result.parsedPath)\n    result.regexStr = tokensToRegexp(result.tokens).source\n  } catch (err) {\n    reportError(result, err)\n    result.error = err\n  }\n\n  return result\n}\n"], "names": ["tryToParsePath", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "isError", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "options", "result", "handleUrl", "parsed", "parseURL", "pathname", "hash", "tokens", "parse", "regexStr", "tokensToRegexp", "source"], "mappings": ";;;;+BA2CgBA;;;eAAAA;;;8BA1CsB;qBACJ;gEACd;;;;;;AAUpB;;;CAGC,GACD,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAe,EAAEC,GAAQ;IAC/D,IAAIC;IACJ,IAAIC,IAAAA,gBAAO,EAACF,QAASC,CAAAA,aAAaD,IAAIG,OAAO,CAACC,KAAK,CAAC,cAAa,GAAI;QACnE,MAAMC,WAAWC,SAASL,UAAU,CAAC,EAAE,EAAE;QACzCM,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEV,MAAM,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,IAAIG,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEJ,WAAW,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIU,MAAMJ,UAAUK,IAAI,CAAC,KAAKC,IAAI,CAAC,IAAI,GAAG,CAAC;IAEtD,OAAO;QACLJ,QAAQC,KAAK,CACX,CAAC,gBAAgB,EAAEV,MAAM,sDAAsD,CAAC,EAChFE;IAEJ;AACF;AASO,SAASJ,eACdE,KAAa,EACbc,OAEC;IAED,MAAMC,SAAsB;QAAEf;QAAOC,YAAYD;IAAM;IACvD,IAAI;QACF,IAAIc,2BAAAA,QAASE,SAAS,EAAE;YACtB,MAAMC,SAASC,IAAAA,UAAQ,EAAClB,OAAO;YAC/Be,OAAOd,UAAU,GAAG,GAAGgB,OAAOE,QAAQ,GAAIF,OAAOG,IAAI,IAAI,IAAI;QAC/D;QAEAL,OAAOM,MAAM,GAAGC,IAAAA,mBAAK,EAACP,OAAOd,UAAU;QACvCc,OAAOQ,QAAQ,GAAGC,IAAAA,4BAAc,EAACT,OAAOM,MAAM,EAAEI,MAAM;IACxD,EAAE,OAAOvB,KAAK;QACZH,YAAYgB,QAAQb;QACpBa,OAAOL,KAAK,GAAGR;IACjB;IAEA,OAAOa;AACT"}