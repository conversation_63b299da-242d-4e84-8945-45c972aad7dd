{"version": 3, "sources": ["../../src/trace/shared.ts"], "sourcesContent": ["import { randomBytes } from 'node:crypto'\n\nlet _traceGlobals: Map<any, any> = (global as any)._traceGlobals\n\nif (!_traceGlobals) {\n  _traceGlobals = new Map()\n}\n;(global as any)._traceGlobals = _traceGlobals\n\nexport const traceGlobals: Map<any, any> = _traceGlobals\nexport const setGlobal = (key: any, val: any) => {\n  traceGlobals.set(key, val)\n}\n\nexport const traceId =\n  process.env.TRACE_ID ||\n  process.env.NEXT_PRIVATE_TRACE_ID ||\n  randomBytes(8).toString('hex')\n"], "names": ["setGlobal", "traceGlobals", "traceId", "_traceGlobals", "global", "Map", "key", "val", "set", "process", "env", "TRACE_ID", "NEXT_PRIVATE_TRACE_ID", "randomBytes", "toString"], "mappings": ";;;;;;;;;;;;;;;;IAUaA,SAAS;eAATA;;IADAC,YAAY;eAAZA;;IAKAC,OAAO;eAAPA;;;4BAde;AAE5B,IAAIC,gBAA+B,AAACC,OAAeD,aAAa;AAEhE,IAAI,CAACA,eAAe;IAClBA,gBAAgB,IAAIE;AACtB;AACED,OAAeD,aAAa,GAAGA;AAE1B,MAAMF,eAA8BE;AACpC,MAAMH,YAAY,CAACM,KAAUC;IAClCN,aAAaO,GAAG,CAACF,KAAKC;AACxB;AAEO,MAAML,UACXO,QAAQC,GAAG,CAACC,QAAQ,IACpBF,QAAQC,GAAG,CAACE,qBAAqB,IACjCC,IAAAA,uBAAW,EAAC,GAAGC,QAAQ,CAAC"}