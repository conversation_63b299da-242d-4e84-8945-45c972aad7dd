{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "sourcesContent": ["import type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { BinaryStreamOf } from './app-render'\n\nimport { htmlEscapeJsonString } from '../htmlescape'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst INLINE_FLIGHT_PAYLOAD_BOOTSTRAP = 0\nconst INLINE_FLIGHT_PAYLOAD_DATA = 1\nconst INLINE_FLIGHT_PAYLOAD_FORM_STATE = 2\nconst INLINE_FLIGHT_PAYLOAD_BINARY = 3\n\nconst flightResponses = new WeakMap<BinaryStreamOf<any>, Promise<any>>()\nconst encoder = new TextEncoder()\n\n/**\n * Render Flight stream.\n * This is only used for renderToHTML, the Flight response does not need additional wrappers.\n */\nexport function useFlightStream<T>(\n  flightStream: BinaryStreamOf<T>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>,\n  nonce?: string\n): Promise<T> {\n  const response = flightResponses.get(flightStream)\n\n  if (response) {\n    return response\n  }\n\n  // react-server-dom-webpack/client.edge must not be hoisted for require cache clearing to work correctly\n  const { createFromReadableStream } =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client.edge') as typeof import('react-server-dom-webpack/client.edge')\n\n  const newResponse = createFromReadableStream<T>(flightStream, {\n    serverConsumerManifest: {\n      moduleLoading: clientReferenceManifest.moduleLoading,\n      moduleMap: isEdgeRuntime\n        ? clientReferenceManifest.edgeSSRModuleMapping\n        : clientReferenceManifest.ssrModuleMapping,\n      serverModuleMap: null,\n    },\n    nonce,\n  })\n\n  flightResponses.set(flightStream, newResponse)\n\n  return newResponse\n}\n\n/**\n * Creates a ReadableStream provides inline script tag chunks for writing hydration\n * data to the client outside the React render itself.\n *\n * @param flightStream The RSC render stream\n * @param nonce optionally a nonce used during this particular render\n * @param formState optionally the formState used with this particular render\n * @returns a ReadableStream without the complete property. This signifies a lazy ReadableStream\n */\nexport function createInlinedDataReadableStream(\n  flightStream: ReadableStream<Uint8Array>,\n  nonce: string | undefined,\n  formState: unknown | null\n): ReadableStream<Uint8Array> {\n  const startScriptTag = nonce\n    ? `<script nonce=${JSON.stringify(nonce)}>`\n    : '<script>'\n\n  const flightReader = flightStream.getReader()\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n\n  const readable = new ReadableStream({\n    type: 'bytes',\n    start(controller) {\n      try {\n        writeInitialInstructions(controller, startScriptTag, formState)\n      } catch (error) {\n        // during encoding or enqueueing forward the error downstream\n        controller.error(error)\n      }\n    },\n    async pull(controller) {\n      try {\n        const { done, value } = await flightReader.read()\n\n        if (value) {\n          try {\n            const decodedString = decoder.decode(value, { stream: !done })\n\n            // The chunk cannot be decoded as valid UTF-8 string as it might\n            // have arbitrary binary data.\n            writeFlightDataInstruction(\n              controller,\n              startScriptTag,\n              decodedString\n            )\n          } catch {\n            // The chunk cannot be decoded as valid UTF-8 string.\n            writeFlightDataInstruction(controller, startScriptTag, value)\n          }\n        }\n\n        if (done) {\n          controller.close()\n        }\n      } catch (error) {\n        // There was a problem in the upstream reader or during decoding or enqueuing\n        // forward the error downstream\n        controller.error(error)\n      }\n    },\n  })\n\n  return readable\n}\n\nfunction writeInitialInstructions(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  formState: unknown | null\n) {\n  if (formState != null) {\n    controller.enqueue(\n      encoder.encode(\n        `${scriptStart}(self.__next_f=self.__next_f||[]).push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_BOOTSTRAP])\n        )});self.__next_f.push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_FORM_STATE, formState])\n        )})</script>`\n      )\n    )\n  } else {\n    controller.enqueue(\n      encoder.encode(\n        `${scriptStart}(self.__next_f=self.__next_f||[]).push(${htmlEscapeJsonString(\n          JSON.stringify([INLINE_FLIGHT_PAYLOAD_BOOTSTRAP])\n        )})</script>`\n      )\n    )\n  }\n}\n\nfunction writeFlightDataInstruction(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  chunk: string | Uint8Array\n) {\n  let htmlInlinedData: string\n\n  if (typeof chunk === 'string') {\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_DATA, chunk])\n    )\n  } else {\n    // The chunk cannot be embedded as a UTF-8 string in the script tag.\n    // Instead let's inline it in base64.\n    // Credits to Devon Govett (devongovett) for the technique.\n    // https://github.com/devongovett/rsc-html-stream\n    const base64 = btoa(String.fromCodePoint(...chunk))\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_BINARY, base64])\n    )\n  }\n\n  controller.enqueue(\n    encoder.encode(\n      `${scriptStart}self.__next_f.push(${htmlInlinedData})</script>`\n    )\n  )\n}\n"], "names": ["createInlinedDataReadableStream", "useFlightStream", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "INLINE_FLIGHT_PAYLOAD_BINARY", "flightResponses", "WeakMap", "encoder", "TextEncoder", "flightStream", "clientReferenceManifest", "nonce", "response", "get", "createFromReadableStream", "require", "newResponse", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "serverModuleMap", "set", "formState", "startScriptTag", "JSON", "stringify", "flightReader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "fatal", "readable", "ReadableStream", "type", "start", "controller", "writeInitialInstructions", "error", "pull", "done", "value", "read", "decodedString", "decode", "stream", "writeFlightDataInstruction", "close", "scriptStart", "enqueue", "encode", "htmlEscapeJsonString", "chunk", "htmlInlinedData", "base64", "btoa", "String", "fromCodePoint"], "mappings": ";;;;;;;;;;;;;;;IA6DgBA,+BAA+B;eAA/BA;;IAzCAC,eAAe;eAAfA;;;4BAjBqB;AAGrC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AACzC,MAAMC,+BAA+B;AAErC,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAMb,SAASZ,gBACda,YAA+B,EAC/BC,uBAA8D,EAC9DC,KAAc;IAEd,MAAMC,WAAWP,gBAAgBQ,GAAG,CAACJ;IAErC,IAAIG,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,MAAM,EAAEE,wBAAwB,EAAE,GAChC,6DAA6D;IAC7DC,QAAQ;IAEV,MAAMC,cAAcF,yBAA4BL,cAAc;QAC5DQ,wBAAwB;YACtBC,eAAeR,wBAAwBQ,aAAa;YACpDC,WAAWtB,gBACPa,wBAAwBU,oBAAoB,GAC5CV,wBAAwBW,gBAAgB;YAC5CC,iBAAiB;QACnB;QACAX;IACF;IAEAN,gBAAgBkB,GAAG,CAACd,cAAcO;IAElC,OAAOA;AACT;AAWO,SAASrB,gCACdc,YAAwC,EACxCE,KAAyB,EACzBa,SAAyB;IAEzB,MAAMC,iBAAiBd,QACnB,CAAC,cAAc,EAAEe,KAAKC,SAAS,CAAChB,OAAO,CAAC,CAAC,GACzC;IAEJ,MAAMiB,eAAenB,aAAaoB,SAAS;IAC3C,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IAEvD,MAAMC,WAAW,IAAIC,eAAe;QAClCC,MAAM;QACNC,OAAMC,UAAU;YACd,IAAI;gBACFC,yBAAyBD,YAAYZ,gBAAgBD;YACvD,EAAE,OAAOe,OAAO;gBACd,6DAA6D;gBAC7DF,WAAWE,KAAK,CAACA;YACnB;QACF;QACA,MAAMC,MAAKH,UAAU;YACnB,IAAI;gBACF,MAAM,EAAEI,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMd,aAAae,IAAI;gBAE/C,IAAID,OAAO;oBACT,IAAI;wBACF,MAAME,gBAAgBd,QAAQe,MAAM,CAACH,OAAO;4BAAEI,QAAQ,CAACL;wBAAK;wBAE5D,gEAAgE;wBAChE,8BAA8B;wBAC9BM,2BACEV,YACAZ,gBACAmB;oBAEJ,EAAE,OAAM;wBACN,qDAAqD;wBACrDG,2BAA2BV,YAAYZ,gBAAgBiB;oBACzD;gBACF;gBAEA,IAAID,MAAM;oBACRJ,WAAWW,KAAK;gBAClB;YACF,EAAE,OAAOT,OAAO;gBACd,6EAA6E;gBAC7E,+BAA+B;gBAC/BF,WAAWE,KAAK,CAACA;YACnB;QACF;IACF;IAEA,OAAON;AACT;AAEA,SAASK,yBACPD,UAA2C,EAC3CY,WAAmB,EACnBzB,SAAyB;IAEzB,IAAIA,aAAa,MAAM;QACrBa,WAAWa,OAAO,CAChB3C,QAAQ4C,MAAM,CACZ,GAAGF,YAAY,uCAAuC,EAAEG,IAAAA,gCAAoB,EAC1E1B,KAAKC,SAAS,CAAC;YAAC1B;SAAgC,GAChD,qBAAqB,EAAEmD,IAAAA,gCAAoB,EAC3C1B,KAAKC,SAAS,CAAC;YAACxB;YAAkCqB;SAAU,GAC5D,UAAU,CAAC;IAGnB,OAAO;QACLa,WAAWa,OAAO,CAChB3C,QAAQ4C,MAAM,CACZ,GAAGF,YAAY,uCAAuC,EAAEG,IAAAA,gCAAoB,EAC1E1B,KAAKC,SAAS,CAAC;YAAC1B;SAAgC,GAChD,UAAU,CAAC;IAGnB;AACF;AAEA,SAAS8C,2BACPV,UAA2C,EAC3CY,WAAmB,EACnBI,KAA0B;IAE1B,IAAIC;IAEJ,IAAI,OAAOD,UAAU,UAAU;QAC7BC,kBAAkBF,IAAAA,gCAAoB,EACpC1B,KAAKC,SAAS,CAAC;YAACzB;YAA4BmD;SAAM;IAEtD,OAAO;QACL,oEAAoE;QACpE,qCAAqC;QACrC,2DAA2D;QAC3D,iDAAiD;QACjD,MAAME,SAASC,KAAKC,OAAOC,aAAa,IAAIL;QAC5CC,kBAAkBF,IAAAA,gCAAoB,EACpC1B,KAAKC,SAAS,CAAC;YAACvB;YAA8BmD;SAAO;IAEzD;IAEAlB,WAAWa,OAAO,CAChB3C,QAAQ4C,MAAM,CACZ,GAAGF,YAAY,mBAAmB,EAAEK,gBAAgB,UAAU,CAAC;AAGrE"}