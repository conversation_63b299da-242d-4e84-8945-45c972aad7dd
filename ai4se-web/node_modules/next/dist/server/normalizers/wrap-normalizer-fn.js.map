{"version": 3, "sources": ["../../../src/server/normalizers/wrap-normalizer-fn.ts"], "sourcesContent": ["import type { Normalizer } from './normalizer'\n\nexport function wrapNormalizerFn(fn: (pathname: string) => string): Normalizer {\n  return { normalize: fn }\n}\n"], "names": ["wrapNormalizerFn", "fn", "normalize"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,iBAAiBC,EAAgC;IAC/D,OAAO;QAAEC,WAAWD;IAAG;AACzB"}