{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/tags-manifest.external.ts"], "sourcesContent": ["import type { Timestamp } from '../cache-handlers/types'\n\n// We share the tags manifest between the \"use cache\" handlers and the previous\n// file-system cache.\nexport const tagsManifest = new Map<string, number>()\n\nexport const isStale = (tags: string[], timestamp: Timestamp) => {\n  for (const tag of tags) {\n    const revalidatedAt = tagsManifest.get(tag)\n\n    if (typeof revalidatedAt === 'number' && revalidatedAt >= timestamp) {\n      return true\n    }\n  }\n\n  return false\n}\n"], "names": ["isStale", "tagsManifest", "Map", "tags", "timestamp", "tag", "revalidatedAt", "get"], "mappings": ";;;;;;;;;;;;;;;IAMaA,OAAO;eAAPA;;IAFAC,YAAY;eAAZA;;;AAAN,MAAMA,eAAe,IAAIC;AAEzB,MAAMF,UAAU,CAACG,MAAgBC;IACtC,KAAK,MAAMC,OAAOF,KAAM;QACtB,MAAMG,gBAAgBL,aAAaM,GAAG,CAACF;QAEvC,IAAI,OAAOC,kBAAkB,YAAYA,iBAAiBF,WAAW;YACnE,OAAO;QACT;IACF;IAEA,OAAO;AACT"}