{"version": 3, "sources": ["../../../../src/server/lib/router-utils/setup-dev-bundler.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../config-shared'\nimport type { FilesystemDynamicRoute } from './filesystem'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport {\n  getPageStaticInfo,\n  type MiddlewareMatcher,\n} from '../../../build/analysis/get-page-static-info'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport type { PropagateToWorkersField } from './types'\nimport type { NextJsHotReloaderInterface } from '../../dev/hot-reloader-types'\n\nimport { createDefineEnv } from '../../../build/swc'\nimport fs from 'fs'\nimport { mkdir } from 'fs/promises'\nimport url from 'url'\nimport path from 'path'\nimport qs from 'querystring'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport { loadEnvConfig } from '@next/env'\nimport findUp from 'next/dist/compiled/find-up'\nimport { buildCustomRoute } from './filesystem'\nimport * as Log from '../../../build/output/log'\nimport HotReloaderWebpack from '../../dev/hot-reloader-webpack'\nimport { setGlobal } from '../../../trace/shared'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { createValidFileMatcher } from '../find-page-file'\nimport {\n  EVENT_BUILD_FEATURE_USAGE,\n  eventCliSession,\n} from '../../../telemetry/events'\nimport { getDefineEnv } from '../../../build/webpack/plugins/define-env-plugin'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport {\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from '../../../build/entries'\nimport { verifyTypeScriptSetup } from '../../../lib/verify-typescript-setup'\nimport { verifyPartytownSetup } from '../../../lib/verify-partytown-setup'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport { buildDataRoute } from './build-data-route'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { createClientRouterFilter } from '../../../lib/create-client-router-filter'\nimport { absolutePathToPage } from '../../../shared/lib/page-path/absolute-path-to-page'\nimport { generateInterceptionRoutesRewrites } from '../../../lib/generate-interception-routes-rewrites'\n\nimport {\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  PHASE_DEVELOPMENT_SERVER,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../../../shared/lib/constants'\n\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport {\n  isMiddlewareFile,\n  NestedMiddlewareError,\n  isInstrumentationHookFile,\n  getPossibleMiddlewareFilenames,\n  getPossibleInstrumentationHookFilenames,\n} from '../../../build/utils'\nimport { devPageFiles } from '../../../build/webpack/plugins/next-types-plugin/shared'\nimport type { LazyRenderServerInstance } from '../router-server'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../dev/hot-reloader-types'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { createHotReloaderTurbopack } from '../../dev/hot-reloader-turbopack'\nimport { generateEncryptionKeyBase64 } from '../../app-render/encryption-utils-server'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport { createEnvDefinitions } from '../experimental/create-env-definitions'\nimport { JsConfigPathsPlugin } from '../../../build/webpack/plugins/jsconfig-paths-plugin'\nimport { store as consoleStore } from '../../../build/output/store'\nimport {\n  isPersistentCachingEnabled,\n  ModuleBuildError,\n  TurbopackInternalError,\n} from '../../../shared/lib/turbopack/utils'\n\nexport type SetupOpts = {\n  renderServer: LazyRenderServerInstance\n  dir: string\n  turbo?: boolean\n  appDir?: string\n  pagesDir?: string\n  telemetry: Telemetry\n  isCustomServer?: boolean\n  fsChecker: UnwrapPromise<\n    ReturnType<typeof import('./filesystem').setupFsCheck>\n  >\n  nextConfig: NextConfigComplete\n  port: number\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  resetFetch: () => void\n}\n\nexport type ServerFields = {\n  actualMiddlewareFile?: string | undefined\n  actualInstrumentationHookFile?: string | undefined\n  appPathRoutes?: Record<string, string | string[]>\n  middleware?:\n    | {\n        page: string\n        match: MiddlewareRouteMatch\n        matchers?: MiddlewareMatcher[]\n      }\n    | undefined\n  hasAppNotFound?: boolean\n  interceptionRoutes?: ReturnType<\n    typeof import('./filesystem').buildCustomRoute\n  >[]\n  setIsrStatus?: (key: string, value: boolean) => void\n  resetFetch?: () => void\n}\n\nasync function verifyTypeScript(opts: SetupOpts) {\n  let usingTypeScript = false\n  const verifyResult = await verifyTypeScriptSetup({\n    dir: opts.dir,\n    distDir: opts.nextConfig.distDir,\n    intentDirs: [opts.pagesDir, opts.appDir].filter(Boolean) as string[],\n    typeCheckPreflight: false,\n    tsconfigPath: opts.nextConfig.typescript.tsconfigPath,\n    disableStaticImages: opts.nextConfig.images.disableStaticImages,\n    hasAppDir: !!opts.appDir,\n    hasPagesDir: !!opts.pagesDir,\n  })\n\n  if (verifyResult.version) {\n    usingTypeScript = true\n  }\n  return usingTypeScript\n}\n\nexport async function propagateServerField(\n  opts: SetupOpts,\n  field: PropagateToWorkersField,\n  args: any\n) {\n  await opts.renderServer?.instance?.propagateServerField(opts.dir, field, args)\n}\n\nasync function startWatcher(opts: SetupOpts) {\n  const { nextConfig, appDir, pagesDir, dir, resetFetch } = opts\n  const { useFileSystemPublicRoutes } = nextConfig\n  const usingTypeScript = await verifyTypeScript(opts)\n\n  const distDir = path.join(opts.dir, opts.nextConfig.distDir)\n\n  // we ensure the types directory exists here\n  if (usingTypeScript) {\n    const distTypesDir = path.join(distDir, 'types')\n    if (!fs.existsSync(distTypesDir)) {\n      await mkdir(distTypesDir, { recursive: true })\n    }\n  }\n\n  setGlobal('distDir', distDir)\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n  const validFileMatcher = createValidFileMatcher(\n    nextConfig.pageExtensions,\n    appDir\n  )\n\n  const serverFields: ServerFields = {}\n\n  // Update logging state once based on next.config.js when initializing\n  consoleStore.setState({\n    logging: nextConfig.logging !== false,\n  })\n\n  const hotReloader: NextJsHotReloaderInterface = opts.turbo\n    ? await createHotReloaderTurbopack(opts, serverFields, distDir, resetFetch)\n    : new HotReloaderWebpack(opts.dir, {\n        appDir,\n        pagesDir,\n        distDir,\n        config: opts.nextConfig,\n        buildId: 'development',\n        encryptionKey: await generateEncryptionKeyBase64({\n          isBuild: false,\n          distDir,\n        }),\n        telemetry: opts.telemetry,\n        rewrites: opts.fsChecker.rewrites,\n        previewProps: opts.fsChecker.prerenderManifest.preview,\n        resetFetch,\n      })\n\n  await hotReloader.start()\n\n  if (opts.nextConfig.experimental.nextScriptWorkers) {\n    await verifyPartytownSetup(\n      opts.dir,\n      path.join(distDir, CLIENT_STATIC_FILES_PATH)\n    )\n  }\n\n  opts.fsChecker.ensureCallback(async function ensure(item) {\n    if (item.type === 'appFile' || item.type === 'pageFile') {\n      await hotReloader.ensurePage({\n        clientOnly: false,\n        page: item.itemPath,\n        isApp: item.type === 'appFile',\n        definition: undefined,\n      })\n    }\n  })\n\n  let resolved = false\n  let prevSortedRoutes: string[] = []\n\n  await new Promise<void>(async (resolve, reject) => {\n    if (pagesDir) {\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n    }\n\n    const pages = pagesDir ? [pagesDir] : []\n    const app = appDir ? [appDir] : []\n    const directories = [...pages, ...app]\n\n    const rootDir = pagesDir || appDir\n    const files = [\n      ...getPossibleMiddlewareFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n      ...getPossibleInstrumentationHookFilenames(\n        path.join(rootDir!, '..'),\n        nextConfig.pageExtensions\n      ),\n    ]\n    let nestedMiddleware: string[] = []\n\n    const envFiles = [\n      '.env.development.local',\n      '.env.local',\n      '.env.development',\n      '.env',\n    ].map((file) => path.join(dir, file))\n\n    files.push(...envFiles)\n\n    // tsconfig/jsconfig paths hot-reloading\n    const tsconfigPaths = [\n      path.join(dir, 'tsconfig.json'),\n      path.join(dir, 'jsconfig.json'),\n    ] as const\n    files.push(...tsconfigPaths)\n\n    const wp = new Watchpack({\n      ignored: (pathname: string) => {\n        return (\n          !files.some((file) => file.startsWith(pathname)) &&\n          !directories.some(\n            (d) => pathname.startsWith(d) || d.startsWith(pathname)\n          )\n        )\n      },\n    })\n    const fileWatchTimes = new Map()\n    let enabledTypeScript = usingTypeScript\n    let previousClientRouterFilters: any\n    let previousConflictingPagePaths: Set<string> = new Set()\n\n    wp.on('aggregated', async () => {\n      let middlewareMatchers: MiddlewareMatcher[] | undefined\n      const routedPages: string[] = []\n      const knownFiles = wp.getTimeInfoEntries()\n      const appPaths: Record<string, string[]> = {}\n      const pageNameSet = new Set<string>()\n      const conflictingAppPagePaths = new Set<string>()\n      const appPageFilePaths = new Map<string, string>()\n      const pagesPageFilePaths = new Map<string, string>()\n\n      let envChange = false\n      let tsconfigChange = false\n      let conflictingPageChange = 0\n      let hasRootAppNotFound = false\n\n      const { appFiles, pageFiles } = opts.fsChecker\n\n      appFiles.clear()\n      pageFiles.clear()\n      devPageFiles.clear()\n\n      const sortedKnownFiles: string[] = [...knownFiles.keys()].sort(\n        sortByPageExts(nextConfig.pageExtensions)\n      )\n\n      for (const fileName of sortedKnownFiles) {\n        if (\n          !files.includes(fileName) &&\n          !directories.some((d) => fileName.startsWith(d))\n        ) {\n          continue\n        }\n        const meta = knownFiles.get(fileName)\n\n        const watchTime = fileWatchTimes.get(fileName)\n        // If the file is showing up for the first time or the meta.timestamp is changed since last time\n        const watchTimeChange =\n          watchTime === undefined ||\n          (watchTime && watchTime !== meta?.timestamp)\n        fileWatchTimes.set(fileName, meta?.timestamp)\n\n        if (envFiles.includes(fileName)) {\n          if (watchTimeChange) {\n            envChange = true\n          }\n          continue\n        }\n\n        if (tsconfigPaths.includes(fileName)) {\n          if (fileName.endsWith('tsconfig.json')) {\n            enabledTypeScript = true\n          }\n          if (watchTimeChange) {\n            tsconfigChange = true\n          }\n          continue\n        }\n\n        if (\n          meta?.accuracy === undefined ||\n          !validFileMatcher.isPageFile(fileName)\n        ) {\n          continue\n        }\n\n        const isAppPath = Boolean(\n          appDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(appDir) + '/'\n            )\n        )\n        const isPagePath = Boolean(\n          pagesDir &&\n            normalizePathSep(fileName).startsWith(\n              normalizePathSep(pagesDir) + '/'\n            )\n        )\n\n        const rootFile = absolutePathToPage(fileName, {\n          dir: dir,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: false,\n          pagesType: PAGE_TYPES.ROOT,\n        })\n\n        if (isMiddlewareFile(rootFile)) {\n          const staticInfo = await getStaticInfoIncludingLayouts({\n            pageFilePath: fileName,\n            config: nextConfig,\n            appDir: appDir,\n            page: rootFile,\n            isDev: true,\n            isInsideAppDir: isAppPath,\n            pageExtensions: nextConfig.pageExtensions,\n          })\n          if (nextConfig.output === 'export') {\n            Log.error(\n              'Middleware cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n            continue\n          }\n          serverFields.actualMiddlewareFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualMiddlewareFile',\n            serverFields.actualMiddlewareFile\n          )\n          middlewareMatchers = staticInfo.middleware?.matchers || [\n            { regexp: '.*', originalSource: '/:path*' },\n          ]\n          continue\n        }\n        if (isInstrumentationHookFile(rootFile)) {\n          serverFields.actualInstrumentationHookFile = rootFile\n          await propagateServerField(\n            opts,\n            'actualInstrumentationHookFile',\n            serverFields.actualInstrumentationHookFile\n          )\n          continue\n        }\n\n        if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) {\n          enabledTypeScript = true\n        }\n\n        if (!(isAppPath || isPagePath)) {\n          continue\n        }\n\n        // Collect all current filenames for the TS plugin to use\n        devPageFiles.add(fileName)\n\n        let pageName = absolutePathToPage(fileName, {\n          dir: isAppPath ? appDir! : pagesDir!,\n          extensions: nextConfig.pageExtensions,\n          keepIndex: isAppPath,\n          pagesType: isAppPath ? PAGE_TYPES.APP : PAGE_TYPES.PAGES,\n        })\n\n        if (\n          isAppPath &&\n          appDir &&\n          isMetadataRouteFile(\n            fileName.replace(appDir, ''),\n            nextConfig.pageExtensions,\n            true\n          )\n        ) {\n          const staticInfo = await getPageStaticInfo({\n            pageFilePath: fileName,\n            nextConfig: {},\n            page: pageName,\n            isDev: true,\n            pageType: PAGE_TYPES.APP,\n          })\n\n          pageName = normalizeMetadataPageToRoute(\n            pageName,\n            !!(staticInfo.generateSitemaps || staticInfo.generateImageMetadata)\n          )\n        }\n\n        if (\n          !isAppPath &&\n          pageName.startsWith('/api/') &&\n          nextConfig.output === 'export'\n        ) {\n          Log.error(\n            'API Routes cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n          )\n          continue\n        }\n\n        if (isAppPath) {\n          const isRootNotFound = validFileMatcher.isRootNotFound(fileName)\n          hasRootAppNotFound = true\n\n          if (isRootNotFound) {\n            continue\n          }\n          if (!isRootNotFound && !validFileMatcher.isAppRouterPage(fileName)) {\n            continue\n          }\n          // Ignore files/directories starting with `_` in the app directory\n          if (normalizePathSep(pageName).includes('/_')) {\n            continue\n          }\n\n          const originalPageName = pageName\n          pageName = normalizeAppPath(pageName).replace(/%5F/g, '_')\n          if (!appPaths[pageName]) {\n            appPaths[pageName] = []\n          }\n          appPaths[pageName].push(originalPageName)\n\n          if (useFileSystemPublicRoutes) {\n            appFiles.add(pageName)\n          }\n\n          if (routedPages.includes(pageName)) {\n            continue\n          }\n        } else {\n          if (useFileSystemPublicRoutes) {\n            pageFiles.add(pageName)\n            // always add to nextDataRoutes for now but in future only add\n            // entries that actually use getStaticProps/getServerSideProps\n            opts.fsChecker.nextDataRoutes.add(pageName)\n          }\n        }\n        ;(isAppPath ? appPageFilePaths : pagesPageFilePaths).set(\n          pageName,\n          fileName\n        )\n\n        if (appDir && pageNameSet.has(pageName)) {\n          conflictingAppPagePaths.add(pageName)\n        } else {\n          pageNameSet.add(pageName)\n        }\n\n        /**\n         * If there is a middleware that is not declared in the root we will\n         * warn without adding it so it doesn't make its way into the system.\n         */\n        if (/[\\\\\\\\/]_middleware$/.test(pageName)) {\n          nestedMiddleware.push(pageName)\n          continue\n        }\n\n        routedPages.push(pageName)\n      }\n\n      const numConflicting = conflictingAppPagePaths.size\n      conflictingPageChange = numConflicting - previousConflictingPagePaths.size\n\n      if (conflictingPageChange !== 0) {\n        if (numConflicting > 0) {\n          let errorMessage = `Conflicting app and page file${\n            numConflicting === 1 ? ' was' : 's were'\n          } found, please remove the conflicting files to continue:\\n`\n\n          for (const p of conflictingAppPagePaths) {\n            const appPath = path.relative(dir, appPageFilePaths.get(p)!)\n            const pagesPath = path.relative(dir, pagesPageFilePaths.get(p)!)\n            errorMessage += `  \"${pagesPath}\" - \"${appPath}\"\\n`\n          }\n          hotReloader.setHmrServerError(new Error(errorMessage))\n        } else if (numConflicting === 0) {\n          hotReloader.clearHmrServerError()\n          await propagateServerField(opts, 'reloadMatchers', undefined)\n        }\n      }\n\n      previousConflictingPagePaths = conflictingAppPagePaths\n\n      let clientRouterFilters: any\n      if (nextConfig.experimental.clientRouterFilter) {\n        clientRouterFilters = createClientRouterFilter(\n          Object.keys(appPaths),\n          nextConfig.experimental.clientRouterFilterRedirects\n            ? ((nextConfig as any)._originalRedirects || []).filter(\n                (r: any) => !r.internal\n              )\n            : [],\n          nextConfig.experimental.clientRouterFilterAllowedRate\n        )\n\n        if (\n          !previousClientRouterFilters ||\n          JSON.stringify(previousClientRouterFilters) !==\n            JSON.stringify(clientRouterFilters)\n        ) {\n          envChange = true\n          previousClientRouterFilters = clientRouterFilters\n        }\n      }\n\n      if (!usingTypeScript && enabledTypeScript) {\n        // we tolerate the error here as this is best effort\n        // and the manual install command will be shown\n        await verifyTypeScript(opts)\n          .then(() => {\n            tsconfigChange = true\n          })\n          .catch(() => {})\n      }\n\n      if (envChange || tsconfigChange) {\n        if (envChange) {\n          const { loadedEnvFiles } = loadEnvConfig(\n            dir,\n            process.env.NODE_ENV === 'development',\n            Log,\n            true,\n            (envFilePath) => {\n              Log.info(`Reload env: ${envFilePath}`)\n            }\n          )\n\n          if (usingTypeScript && nextConfig.experimental?.typedEnv) {\n            // do not await, this is not essential for further process\n            createEnvDefinitions({\n              distDir,\n              loadedEnvFiles: [\n                ...loadedEnvFiles,\n                {\n                  path: nextConfig.configFileName,\n                  env: nextConfig.env,\n                  contents: '',\n                },\n              ],\n            })\n          }\n\n          await propagateServerField(opts, 'loadEnvConfig', [\n            { dev: true, forceReload: true, silent: true },\n          ])\n        }\n        let tsconfigResult:\n          | UnwrapPromise<ReturnType<typeof loadJsConfig>>\n          | undefined\n\n        if (tsconfigChange) {\n          try {\n            tsconfigResult = await loadJsConfig(dir, nextConfig)\n          } catch (_) {\n            /* do we want to log if there are syntax errors in tsconfig while editing? */\n          }\n        }\n\n        if (hotReloader.turbopackProject) {\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          await hotReloader.turbopackProject.update({\n            defineEnv: createDefineEnv({\n              isTurbopack: true,\n              clientRouterFilters,\n              config: nextConfig,\n              dev: true,\n              distDir,\n              fetchCacheKeyPrefix:\n                opts.nextConfig.experimental.fetchCacheKeyPrefix,\n              hasRewrites,\n              // TODO: Implement\n              middlewareMatchers: undefined,\n            }),\n          })\n        }\n\n        hotReloader.activeWebpackConfigs?.forEach((config, idx) => {\n          const isClient = idx === 0\n          const isNodeServer = idx === 1\n          const isEdgeServer = idx === 2\n          const hasRewrites =\n            opts.fsChecker.rewrites.afterFiles.length > 0 ||\n            opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n            opts.fsChecker.rewrites.fallback.length > 0\n\n          if (tsconfigChange) {\n            config.resolve?.plugins?.forEach((plugin: any) => {\n              // look for the JsConfigPathsPlugin and update with\n              // the latest paths/baseUrl config\n              if (plugin instanceof JsConfigPathsPlugin && tsconfigResult) {\n                const { resolvedBaseUrl, jsConfig } = tsconfigResult\n                const currentResolvedBaseUrl = plugin.resolvedBaseUrl\n                const resolvedUrlIndex = config.resolve?.modules?.findIndex(\n                  (item) => item === currentResolvedBaseUrl?.baseUrl\n                )\n\n                if (resolvedBaseUrl) {\n                  if (\n                    resolvedBaseUrl.baseUrl !== currentResolvedBaseUrl?.baseUrl\n                  ) {\n                    // remove old baseUrl and add new one\n                    if (resolvedUrlIndex && resolvedUrlIndex > -1) {\n                      config.resolve?.modules?.splice(resolvedUrlIndex, 1)\n                    }\n\n                    // If the resolvedBaseUrl is implicit we only remove the previous value.\n                    // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n                    if (!resolvedBaseUrl.isImplicit) {\n                      config.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n                    }\n                  }\n                }\n\n                if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n                  Object.keys(plugin.paths).forEach((key) => {\n                    delete plugin.paths[key]\n                  })\n                  Object.assign(plugin.paths, jsConfig.compilerOptions.paths)\n                  plugin.resolvedBaseUrl = resolvedBaseUrl\n                }\n              }\n            })\n          }\n\n          if (envChange) {\n            config.plugins?.forEach((plugin: any) => {\n              // we look for the DefinePlugin definitions so we can\n              // update them on the active compilers\n              if (\n                plugin &&\n                typeof plugin.definitions === 'object' &&\n                plugin.definitions.__NEXT_DEFINE_ENV\n              ) {\n                const newDefine = getDefineEnv({\n                  isTurbopack: false,\n                  clientRouterFilters,\n                  config: nextConfig,\n                  dev: true,\n                  distDir,\n                  fetchCacheKeyPrefix:\n                    opts.nextConfig.experimental.fetchCacheKeyPrefix,\n                  hasRewrites,\n                  isClient,\n                  isEdgeServer,\n                  isNodeOrEdgeCompilation: isNodeServer || isEdgeServer,\n                  isNodeServer,\n                  middlewareMatchers: undefined,\n                })\n\n                Object.keys(plugin.definitions).forEach((key) => {\n                  if (!(key in newDefine)) {\n                    delete plugin.definitions[key]\n                  }\n                })\n                Object.assign(plugin.definitions, newDefine)\n              }\n            })\n          }\n        })\n        await hotReloader.invalidate({\n          reloadAfterInvalidation: envChange,\n        })\n      }\n\n      if (nestedMiddleware.length > 0) {\n        Log.error(\n          new NestedMiddlewareError(\n            nestedMiddleware,\n            dir,\n            (pagesDir || appDir)!\n          ).message\n        )\n        nestedMiddleware = []\n      }\n\n      // Make sure to sort parallel routes to make the result deterministic.\n      serverFields.appPathRoutes = Object.fromEntries(\n        Object.entries(appPaths).map(([k, v]) => [k, v.sort()])\n      )\n      await propagateServerField(\n        opts,\n        'appPathRoutes',\n        serverFields.appPathRoutes\n      )\n\n      // TODO: pass this to fsChecker/next-dev-server?\n      serverFields.middleware = middlewareMatchers\n        ? {\n            match: null as any,\n            page: '/',\n            matchers: middlewareMatchers,\n          }\n        : undefined\n\n      await propagateServerField(opts, 'middleware', serverFields.middleware)\n      serverFields.hasAppNotFound = hasRootAppNotFound\n\n      opts.fsChecker.middlewareMatcher = serverFields.middleware?.matchers\n        ? getMiddlewareRouteMatcher(serverFields.middleware?.matchers)\n        : undefined\n\n      const interceptionRoutes = generateInterceptionRoutesRewrites(\n        Object.keys(appPaths),\n        opts.nextConfig.basePath\n      ).map((item) =>\n        buildCustomRoute(\n          'before_files_rewrite',\n          item,\n          opts.nextConfig.basePath,\n          opts.nextConfig.experimental.caseSensitiveRoutes\n        )\n      )\n\n      opts.fsChecker.rewrites.beforeFiles.push(...interceptionRoutes)\n\n      const exportPathMap =\n        (typeof nextConfig.exportPathMap === 'function' &&\n          (await nextConfig.exportPathMap?.(\n            {},\n            {\n              dev: true,\n              dir: opts.dir,\n              outDir: null,\n              distDir: distDir,\n              buildId: 'development',\n            }\n          ))) ||\n        {}\n\n      const exportPathMapEntries = Object.entries(exportPathMap || {})\n\n      if (exportPathMapEntries.length > 0) {\n        opts.fsChecker.exportPathMapRoutes = exportPathMapEntries.map(\n          ([key, value]) =>\n            buildCustomRoute(\n              'before_files_rewrite',\n              {\n                source: key,\n                destination: `${value.page}${\n                  value.query ? '?' : ''\n                }${qs.stringify(value.query)}`,\n              },\n              opts.nextConfig.basePath,\n              opts.nextConfig.experimental.caseSensitiveRoutes\n            )\n        )\n      }\n\n      try {\n        // we serve a separate manifest with all pages for the client in\n        // dev mode so that we can match a page after a rewrite on the client\n        // before it has been built and is populated in the _buildManifest\n        const sortedRoutes = getSortedRoutes(routedPages)\n\n        opts.fsChecker.dynamicRoutes = sortedRoutes.map(\n          (page): FilesystemDynamicRoute => {\n            const regex = getRouteRegex(page)\n            return {\n              regex: regex.re.toString(),\n              match: getRouteMatcher(regex),\n              page,\n            }\n          }\n        )\n\n        const dataRoutes: typeof opts.fsChecker.dynamicRoutes = []\n\n        for (const page of sortedRoutes) {\n          const route = buildDataRoute(page, 'development')\n          const routeRegex = getRouteRegex(route.page)\n          dataRoutes.push({\n            ...route,\n            regex: routeRegex.re.toString(),\n            match: getRouteMatcher({\n              // TODO: fix this in the manifest itself, must also be fixed in\n              // upstream builder that relies on this\n              re: opts.nextConfig.i18n\n                ? new RegExp(\n                    route.dataRouteRegex.replace(\n                      `/development/`,\n                      `/development/(?<nextLocale>[^/]+?)/`\n                    )\n                  )\n                : new RegExp(route.dataRouteRegex),\n              groups: routeRegex.groups,\n            }),\n          })\n        }\n        opts.fsChecker.dynamicRoutes.unshift(...dataRoutes)\n\n        if (!prevSortedRoutes?.every((val, idx) => val === sortedRoutes[idx])) {\n          const addedRoutes = sortedRoutes.filter(\n            (route) => !prevSortedRoutes.includes(route)\n          )\n          const removedRoutes = prevSortedRoutes.filter(\n            (route) => !sortedRoutes.includes(route)\n          )\n\n          // emit the change so clients fetch the update\n          hotReloader.send({\n            action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE,\n            data: [\n              {\n                devPagesManifest: true,\n              },\n            ],\n          })\n\n          addedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE,\n              data: [route],\n            })\n          })\n\n          removedRoutes.forEach((route) => {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE,\n              data: [route],\n            })\n          })\n        }\n        prevSortedRoutes = sortedRoutes\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      } catch (e) {\n        if (!resolved) {\n          reject(e)\n          resolved = true\n        } else {\n          Log.warn('Failed to reload dynamic routes:', e)\n        }\n      } finally {\n        // Reload the matchers. The filesystem would have been written to,\n        // and the matchers need to re-scan it to update the router.\n        await propagateServerField(opts, 'reloadMatchers', undefined)\n      }\n    })\n\n    wp.watch({ directories: [dir], startTime: 0 })\n  })\n\n  const clientPagesManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_PAGES_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(clientPagesManifestPath)\n\n  const devMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devMiddlewareManifestPath)\n\n  const devTurbopackMiddlewareManifestPath = `/_next/${CLIENT_STATIC_FILES_PATH}/development/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`\n  opts.fsChecker.devVirtualFsItems.add(devTurbopackMiddlewareManifestPath)\n\n  async function requestHandler(req: IncomingMessage, res: ServerResponse) {\n    const parsedUrl = url.parse(req.url || '/')\n\n    if (parsedUrl.pathname?.includes(clientPagesManifestPath)) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(\n        JSON.stringify({\n          pages: prevSortedRoutes.filter(\n            (route) => !opts.fsChecker.appFiles.has(route)\n          ),\n        })\n      )\n      return { finished: true }\n    }\n\n    if (\n      parsedUrl.pathname?.includes(devMiddlewareManifestPath) ||\n      parsedUrl.pathname?.includes(devTurbopackMiddlewareManifestPath)\n    ) {\n      res.statusCode = 200\n      res.setHeader('Content-Type', 'application/json; charset=utf-8')\n      res.end(JSON.stringify(serverFields.middleware?.matchers || []))\n      return { finished: true }\n    }\n    return { finished: false }\n  }\n\n  function logErrorWithOriginalStack(\n    err: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ) {\n    if (err instanceof ModuleBuildError) {\n      // Errors that may come from issues from the user's code\n      Log.error(err.message)\n    } else if (err instanceof TurbopackInternalError) {\n      // An internal Turbopack error that has been handled by next-swc, written\n      // to disk and a simplified message shown to user on the Rust side.\n    } else if (type === 'warning') {\n      Log.warn(err)\n    } else if (type === 'app-dir') {\n      Log.error(err)\n    } else if (type) {\n      Log.error(`${type}:`, err)\n    } else {\n      Log.error(err)\n    }\n  }\n\n  return {\n    serverFields,\n    hotReloader,\n    requestHandler,\n    logErrorWithOriginalStack,\n\n    async ensureMiddleware(requestUrl?: string) {\n      if (!serverFields.actualMiddlewareFile) return\n      return hotReloader.ensurePage({\n        page: serverFields.actualMiddlewareFile,\n        clientOnly: false,\n        definition: undefined,\n        url: requestUrl,\n      })\n    },\n  }\n}\n\nexport async function setupDevBundler(opts: SetupOpts) {\n  const isSrcDir = path\n    .relative(opts.dir, opts.pagesDir || opts.appDir || '')\n    .startsWith('src')\n\n  const result = await startWatcher(opts)\n\n  opts.telemetry.record(\n    eventCliSession(\n      path.join(opts.dir, opts.nextConfig.distDir),\n      opts.nextConfig,\n      {\n        webpackVersion: 5,\n        isSrcDir,\n        turboFlag: !!opts.turbo,\n        cliCommand: 'dev',\n        appDir: !!opts.appDir,\n        pagesDir: !!opts.pagesDir,\n        isCustomServer: !!opts.isCustomServer,\n        hasNowJson: !!(await findUp('now.json', { cwd: opts.dir })),\n      }\n    )\n  )\n\n  // Track build features for dev server here:\n  opts.telemetry.record({\n    eventName: EVENT_BUILD_FEATURE_USAGE,\n    payload: {\n      featureName: 'turbopackPersistentCaching',\n      invocationCount: isPersistentCachingEnabled(opts.nextConfig) ? 1 : 0,\n    },\n  })\n\n  return result\n}\n\nexport type DevBundler = Awaited<ReturnType<typeof setupDevBundler>>\n\n// Returns a trace rewritten through Turbopack's sourcemaps\n"], "names": ["propagateServerField", "setupDevBundler", "verifyTypeScript", "opts", "usingTypeScript", "verifyResult", "verifyTypeScriptSetup", "dir", "distDir", "nextConfig", "intentDirs", "pagesDir", "appDir", "filter", "Boolean", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "version", "field", "args", "renderServer", "instance", "startWatcher", "resetFetch", "useFileSystemPublicRoutes", "path", "join", "distTypesDir", "fs", "existsSync", "mkdir", "recursive", "setGlobal", "PHASE_DEVELOPMENT_SERVER", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "serverFields", "consoleStore", "setState", "logging", "hotReloader", "turbo", "createHotReloaderTurbopack", "HotReloaderWebpack", "config", "buildId", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "telemetry", "rewrites", "fs<PERSON><PERSON><PERSON>", "previewProps", "prerenderManifest", "preview", "start", "experimental", "nextScriptWorkers", "verifyPartytownSetup", "CLIENT_STATIC_FILES_PATH", "ensure<PERSON><PERSON>back", "ensure", "item", "type", "ensurePage", "clientOnly", "page", "itemPath", "isApp", "definition", "undefined", "resolved", "prevSortedRoutes", "Promise", "resolve", "reject", "readdir", "_", "files", "length", "pages", "app", "directories", "rootDir", "getPossibleMiddlewareFilenames", "getPossibleInstrumentationHookFilenames", "nestedMiddleware", "envFiles", "map", "file", "push", "tsconfigPaths", "wp", "Watchpack", "ignored", "pathname", "some", "startsWith", "d", "fileWatchTimes", "Map", "enabledTypeScript", "previousClientRouterFilters", "previousConflictingPagePaths", "Set", "on", "middlewareMatchers", "routedPages", "knownFiles", "getTimeInfoEntries", "appPaths", "pageNameSet", "conflictingAppPagePaths", "appPageFilePaths", "pagesPageFilePaths", "envChange", "tsconfigChange", "conflictingPageChange", "hasRootAppNotFound", "appFiles", "pageFiles", "clear", "devPageFiles", "sortedKnownFiles", "keys", "sort", "sortByPageExts", "fileName", "includes", "meta", "get", "watchTime", "watchTimeChange", "timestamp", "set", "endsWith", "accuracy", "isPageFile", "isAppPath", "normalizePathSep", "isPagePath", "rootFile", "absolutePathToPage", "extensions", "keepIndex", "pagesType", "PAGE_TYPES", "ROOT", "isMiddlewareFile", "staticInfo", "getStaticInfoIncludingLayouts", "pageFilePath", "isDev", "isInsideAppDir", "output", "Log", "error", "actualMiddlewareFile", "middleware", "matchers", "regexp", "originalSource", "isInstrumentationHookFile", "actualInstrumentationHookFile", "add", "pageName", "APP", "PAGES", "isMetadataRouteFile", "replace", "getPageStaticInfo", "pageType", "normalizeMetadataPageToRoute", "generateSitemaps", "generateImageMetadata", "isRootNotFound", "isAppRouterPage", "originalPageName", "normalizeAppPath", "nextDataRoutes", "has", "test", "numConflicting", "size", "errorMessage", "p", "appPath", "relative", "pagesPath", "setHmrServerError", "Error", "clearHmrServerError", "clientRouterFilters", "clientRouterFilter", "createClientRouterFilter", "Object", "clientRouterFilterRedirects", "_originalRedirects", "r", "internal", "clientRouterFilterAllowedRate", "JSON", "stringify", "then", "catch", "loadedEnvFiles", "loadEnvConfig", "process", "env", "NODE_ENV", "env<PERSON><PERSON><PERSON><PERSON>", "info", "typedEnv", "createEnvDefinitions", "configFileName", "contents", "dev", "forceReload", "silent", "tsconfigResult", "loadJsConfig", "turbopackProject", "hasRewrites", "afterFiles", "beforeFiles", "fallback", "update", "defineEnv", "createDefineEnv", "isTurbopack", "fetchCacheKeyPrefix", "activeWebpackConfigs", "for<PERSON>ach", "idx", "isClient", "isNodeServer", "isEdgeServer", "plugins", "plugin", "JsConfigPathsPlugin", "jsConfig", "resolvedBaseUrl", "currentResolvedBaseUrl", "resolvedUrlIndex", "modules", "findIndex", "baseUrl", "splice", "isImplicit", "compilerOptions", "paths", "key", "assign", "definitions", "__NEXT_DEFINE_ENV", "newDefine", "getDefineEnv", "isNodeOrEdgeCompilation", "invalidate", "reloadAfterInvalidation", "NestedMiddlewareError", "message", "appPathRoutes", "fromEntries", "entries", "k", "v", "match", "hasAppNotFound", "middlewareMatcher", "getMiddlewareRouteMatcher", "interceptionRoutes", "generateInterceptionRoutesRewrites", "basePath", "buildCustomRoute", "caseSensitiveRoutes", "exportPathMap", "outDir", "exportPathMapEntries", "exportPathMapRoutes", "value", "source", "destination", "query", "qs", "sortedRoutes", "getSortedRoutes", "dynamicRoutes", "regex", "getRouteRegex", "re", "toString", "getRouteMatcher", "dataRoutes", "route", "buildDataRoute", "routeRegex", "i18n", "RegExp", "dataRouteRegex", "groups", "unshift", "every", "val", "addedRoutes", "removedRoutes", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "DEV_PAGES_MANIFEST_UPDATE", "data", "devPagesManifest", "ADDED_PAGE", "REMOVED_PAGE", "e", "warn", "watch", "startTime", "clientPagesManifestPath", "DEV_CLIENT_PAGES_MANIFEST", "devVirtualFsItems", "devMiddlewareManifestPath", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "devTurbopackMiddlewareManifestPath", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "requestHandler", "req", "res", "parsedUrl", "url", "parse", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "end", "finished", "logErrorWithOriginalStack", "err", "ModuleBuildError", "TurbopackInternalError", "ensureMiddleware", "requestUrl", "isSrcDir", "result", "record", "eventCliSession", "webpackVersion", "turboFlag", "cliCommand", "isCustomServer", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "featureName", "invocationCount", "isPersistentCachingEnabled"], "mappings": ";;;;;;;;;;;;;;;IA0IsBA,oBAAoB;eAApBA;;IAy0BAC,eAAe;eAAfA;;;mCA78Bf;qBAKyB;2DACjB;0BACO;4DACN;6DACC;oEACF;kEACO;qBACQ;+DACX;4BACc;6DACZ;2EACU;wBACL;qEAGD;8BACc;wBAIhC;iCACsB;uBACG;yBAIzB;uCAC+B;sCACD;4BACP;0BACG;gCACF;8BACC;kCACC;0CACQ;oCACN;oDACgB;2BAQ5C;wCAEmC;wBAQnC;yBACsB;kCAEe;2BACjB;sCACgB;uCACC;iCACR;kCACS;sCACR;qCACD;uBACE;wBAK/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCP,eAAeC,iBAAiBC,IAAe;IAC7C,IAAIC,kBAAkB;IACtB,MAAMC,eAAe,MAAMC,IAAAA,4CAAqB,EAAC;QAC/CC,KAAKJ,KAAKI,GAAG;QACbC,SAASL,KAAKM,UAAU,CAACD,OAAO;QAChCE,YAAY;YAACP,KAAKQ,QAAQ;YAAER,KAAKS,MAAM;SAAC,CAACC,MAAM,CAACC;QAChDC,oBAAoB;QACpBC,cAAcb,KAAKM,UAAU,CAACQ,UAAU,CAACD,YAAY;QACrDE,qBAAqBf,KAAKM,UAAU,CAACU,MAAM,CAACD,mBAAmB;QAC/DE,WAAW,CAAC,CAACjB,KAAKS,MAAM;QACxBS,aAAa,CAAC,CAAClB,KAAKQ,QAAQ;IAC9B;IAEA,IAAIN,aAAaiB,OAAO,EAAE;QACxBlB,kBAAkB;IACpB;IACA,OAAOA;AACT;AAEO,eAAeJ,qBACpBG,IAAe,EACfoB,KAA8B,EAC9BC,IAAS;QAEHrB,6BAAAA;IAAN,QAAMA,qBAAAA,KAAKsB,YAAY,sBAAjBtB,8BAAAA,mBAAmBuB,QAAQ,qBAA3BvB,4BAA6BH,oBAAoB,CAACG,KAAKI,GAAG,EAAEgB,OAAOC;AAC3E;AAEA,eAAeG,aAAaxB,IAAe;IACzC,MAAM,EAAEM,UAAU,EAAEG,MAAM,EAAED,QAAQ,EAAEJ,GAAG,EAAEqB,UAAU,EAAE,GAAGzB;IAC1D,MAAM,EAAE0B,yBAAyB,EAAE,GAAGpB;IACtC,MAAML,kBAAkB,MAAMF,iBAAiBC;IAE/C,MAAMK,UAAUsB,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO;IAE3D,4CAA4C;IAC5C,IAAIJ,iBAAiB;QACnB,MAAM4B,eAAeF,aAAI,CAACC,IAAI,CAACvB,SAAS;QACxC,IAAI,CAACyB,WAAE,CAACC,UAAU,CAACF,eAAe;YAChC,MAAMG,IAAAA,eAAK,EAACH,cAAc;gBAAEI,WAAW;YAAK;QAC9C;IACF;IAEAC,IAAAA,iBAAS,EAAC,WAAW7B;IACrB6B,IAAAA,iBAAS,EAAC,SAASC,mCAAwB;IAE3C,MAAMC,mBAAmBC,IAAAA,oCAAsB,EAC7C/B,WAAWgC,cAAc,EACzB7B;IAGF,MAAM8B,eAA6B,CAAC;IAEpC,sEAAsE;IACtEC,YAAY,CAACC,QAAQ,CAAC;QACpBC,SAASpC,WAAWoC,OAAO,KAAK;IAClC;IAEA,MAAMC,cAA0C3C,KAAK4C,KAAK,GACtD,MAAMC,IAAAA,gDAA0B,EAAC7C,MAAMuC,cAAclC,SAASoB,cAC9D,IAAIqB,2BAAkB,CAAC9C,KAAKI,GAAG,EAAE;QAC/BK;QACAD;QACAH;QACA0C,QAAQ/C,KAAKM,UAAU;QACvB0C,SAAS;QACTC,eAAe,MAAMC,IAAAA,kDAA2B,EAAC;YAC/CC,SAAS;YACT9C;QACF;QACA+C,WAAWpD,KAAKoD,SAAS;QACzBC,UAAUrD,KAAKsD,SAAS,CAACD,QAAQ;QACjCE,cAAcvD,KAAKsD,SAAS,CAACE,iBAAiB,CAACC,OAAO;QACtDhC;IACF;IAEJ,MAAMkB,YAAYe,KAAK;IAEvB,IAAI1D,KAAKM,UAAU,CAACqD,YAAY,CAACC,iBAAiB,EAAE;QAClD,MAAMC,IAAAA,0CAAoB,EACxB7D,KAAKI,GAAG,EACRuB,aAAI,CAACC,IAAI,CAACvB,SAASyD,mCAAwB;IAE/C;IAEA9D,KAAKsD,SAAS,CAACS,cAAc,CAAC,eAAeC,OAAOC,IAAI;QACtD,IAAIA,KAAKC,IAAI,KAAK,aAAaD,KAAKC,IAAI,KAAK,YAAY;YACvD,MAAMvB,YAAYwB,UAAU,CAAC;gBAC3BC,YAAY;gBACZC,MAAMJ,KAAKK,QAAQ;gBACnBC,OAAON,KAAKC,IAAI,KAAK;gBACrBM,YAAYC;YACd;QACF;IACF;IAEA,IAAIC,WAAW;IACf,IAAIC,mBAA6B,EAAE;IAEnC,MAAM,IAAIC,QAAc,OAAOC,SAASC;QACtC,IAAItE,UAAU;YACZ,yDAAyD;YACzDsB,WAAE,CAACiD,OAAO,CAACvE,UAAU,CAACwE,GAAGC;gBACvB,IAAIA,yBAAAA,MAAOC,MAAM,EAAE;oBACjB;gBACF;gBAEA,IAAI,CAACR,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF;QACF;QAEA,MAAMS,QAAQ3E,WAAW;YAACA;SAAS,GAAG,EAAE;QACxC,MAAM4E,MAAM3E,SAAS;YAACA;SAAO,GAAG,EAAE;QAClC,MAAM4E,cAAc;eAAIF;eAAUC;SAAI;QAEtC,MAAME,UAAU9E,YAAYC;QAC5B,MAAMwE,QAAQ;eACTM,IAAAA,sCAA8B,EAC/B5D,aAAI,CAACC,IAAI,CAAC0D,SAAU,OACpBhF,WAAWgC,cAAc;eAExBkD,IAAAA,+CAAuC,EACxC7D,aAAI,CAACC,IAAI,CAAC0D,SAAU,OACpBhF,WAAWgC,cAAc;SAE5B;QACD,IAAImD,mBAA6B,EAAE;QAEnC,MAAMC,WAAW;YACf;YACA;YACA;YACA;SACD,CAACC,GAAG,CAAC,CAACC,OAASjE,aAAI,CAACC,IAAI,CAACxB,KAAKwF;QAE/BX,MAAMY,IAAI,IAAIH;QAEd,wCAAwC;QACxC,MAAMI,gBAAgB;YACpBnE,aAAI,CAACC,IAAI,CAACxB,KAAK;YACfuB,aAAI,CAACC,IAAI,CAACxB,KAAK;SAChB;QACD6E,MAAMY,IAAI,IAAIC;QAEd,MAAMC,KAAK,IAAIC,kBAAS,CAAC;YACvBC,SAAS,CAACC;gBACR,OACE,CAACjB,MAAMkB,IAAI,CAAC,CAACP,OAASA,KAAKQ,UAAU,CAACF,cACtC,CAACb,YAAYc,IAAI,CACf,CAACE,IAAMH,SAASE,UAAU,CAACC,MAAMA,EAAED,UAAU,CAACF;YAGpD;QACF;QACA,MAAMI,iBAAiB,IAAIC;QAC3B,IAAIC,oBAAoBvG;QACxB,IAAIwG;QACJ,IAAIC,+BAA4C,IAAIC;QAEpDZ,GAAGa,EAAE,CAAC,cAAc;gBA2diBrE,0BACLA;YA3d9B,IAAIsE;YACJ,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAahB,GAAGiB,kBAAkB;YACxC,MAAMC,WAAqC,CAAC;YAC5C,MAAMC,cAAc,IAAIP;YACxB,MAAMQ,0BAA0B,IAAIR;YACpC,MAAMS,mBAAmB,IAAIb;YAC7B,MAAMc,qBAAqB,IAAId;YAE/B,IAAIe,YAAY;YAChB,IAAIC,iBAAiB;YACrB,IAAIC,wBAAwB;YAC5B,IAAIC,qBAAqB;YAEzB,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAG3H,KAAKsD,SAAS;YAE9CoE,SAASE,KAAK;YACdD,UAAUC,KAAK;YACfC,qBAAY,CAACD,KAAK;YAElB,MAAME,mBAA6B;mBAAIf,WAAWgB,IAAI;aAAG,CAACC,IAAI,CAC5DC,IAAAA,uBAAc,EAAC3H,WAAWgC,cAAc;YAG1C,KAAK,MAAM4F,YAAYJ,iBAAkB;gBACvC,IACE,CAAC7C,MAAMkD,QAAQ,CAACD,aAChB,CAAC7C,YAAYc,IAAI,CAAC,CAACE,IAAM6B,SAAS9B,UAAU,CAACC,KAC7C;oBACA;gBACF;gBACA,MAAM+B,OAAOrB,WAAWsB,GAAG,CAACH;gBAE5B,MAAMI,YAAYhC,eAAe+B,GAAG,CAACH;gBACrC,gGAAgG;gBAChG,MAAMK,kBACJD,cAAc7D,aACb6D,aAAaA,eAAcF,wBAAAA,KAAMI,SAAS;gBAC7ClC,eAAemC,GAAG,CAACP,UAAUE,wBAAAA,KAAMI,SAAS;gBAE5C,IAAI9C,SAASyC,QAAQ,CAACD,WAAW;oBAC/B,IAAIK,iBAAiB;wBACnBjB,YAAY;oBACd;oBACA;gBACF;gBAEA,IAAIxB,cAAcqC,QAAQ,CAACD,WAAW;oBACpC,IAAIA,SAASQ,QAAQ,CAAC,kBAAkB;wBACtClC,oBAAoB;oBACtB;oBACA,IAAI+B,iBAAiB;wBACnBhB,iBAAiB;oBACnB;oBACA;gBACF;gBAEA,IACEa,CAAAA,wBAAAA,KAAMO,QAAQ,MAAKlE,aACnB,CAACrC,iBAAiBwG,UAAU,CAACV,WAC7B;oBACA;gBACF;gBAEA,MAAMW,YAAYlI,QAChBF,UACEqI,IAAAA,kCAAgB,EAACZ,UAAU9B,UAAU,CACnC0C,IAAAA,kCAAgB,EAACrI,UAAU;gBAGjC,MAAMsI,aAAapI,QACjBH,YACEsI,IAAAA,kCAAgB,EAACZ,UAAU9B,UAAU,CACnC0C,IAAAA,kCAAgB,EAACtI,YAAY;gBAInC,MAAMwI,WAAWC,IAAAA,sCAAkB,EAACf,UAAU;oBAC5C9H,KAAKA;oBACL8I,YAAY5I,WAAWgC,cAAc;oBACrC6G,WAAW;oBACXC,WAAWC,qBAAU,CAACC,IAAI;gBAC5B;gBAEA,IAAIC,IAAAA,wBAAgB,EAACP,WAAW;wBAsBTQ;oBArBrB,MAAMA,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;wBACrDC,cAAcxB;wBACdnF,QAAQzC;wBACRG,QAAQA;wBACR4D,MAAM2E;wBACNW,OAAO;wBACPC,gBAAgBf;wBAChBvG,gBAAgBhC,WAAWgC,cAAc;oBAC3C;oBACA,IAAIhC,WAAWuJ,MAAM,KAAK,UAAU;wBAClCC,KAAIC,KAAK,CACP;wBAEF;oBACF;oBACAxH,aAAayH,oBAAoB,GAAGhB;oBACpC,MAAMnJ,qBACJG,MACA,wBACAuC,aAAayH,oBAAoB;oBAEnCnD,qBAAqB2C,EAAAA,yBAAAA,WAAWS,UAAU,qBAArBT,uBAAuBU,QAAQ,KAAI;wBACtD;4BAAEC,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBACD;gBACF;gBACA,IAAIC,IAAAA,iCAAyB,EAACrB,WAAW;oBACvCzG,aAAa+H,6BAA6B,GAAGtB;oBAC7C,MAAMnJ,qBACJG,MACA,iCACAuC,aAAa+H,6BAA6B;oBAE5C;gBACF;gBAEA,IAAIpC,SAASQ,QAAQ,CAAC,UAAUR,SAASQ,QAAQ,CAAC,SAAS;oBACzDlC,oBAAoB;gBACtB;gBAEA,IAAI,CAAEqC,CAAAA,aAAaE,UAAS,GAAI;oBAC9B;gBACF;gBAEA,yDAAyD;gBACzDlB,qBAAY,CAAC0C,GAAG,CAACrC;gBAEjB,IAAIsC,WAAWvB,IAAAA,sCAAkB,EAACf,UAAU;oBAC1C9H,KAAKyI,YAAYpI,SAAUD;oBAC3B0I,YAAY5I,WAAWgC,cAAc;oBACrC6G,WAAWN;oBACXO,WAAWP,YAAYQ,qBAAU,CAACoB,GAAG,GAAGpB,qBAAU,CAACqB,KAAK;gBAC1D;gBAEA,IACE7B,aACApI,UACAkK,IAAAA,oCAAmB,EACjBzC,SAAS0C,OAAO,CAACnK,QAAQ,KACzBH,WAAWgC,cAAc,EACzB,OAEF;oBACA,MAAMkH,aAAa,MAAMqB,IAAAA,oCAAiB,EAAC;wBACzCnB,cAAcxB;wBACd5H,YAAY,CAAC;wBACb+D,MAAMmG;wBACNb,OAAO;wBACPmB,UAAUzB,qBAAU,CAACoB,GAAG;oBAC1B;oBAEAD,WAAWO,IAAAA,8CAA4B,EACrCP,UACA,CAAC,CAAEhB,CAAAA,WAAWwB,gBAAgB,IAAIxB,WAAWyB,qBAAqB,AAAD;gBAErE;gBAEA,IACE,CAACpC,aACD2B,SAASpE,UAAU,CAAC,YACpB9F,WAAWuJ,MAAM,KAAK,UACtB;oBACAC,KAAIC,KAAK,CACP;oBAEF;gBACF;gBAEA,IAAIlB,WAAW;oBACb,MAAMqC,iBAAiB9I,iBAAiB8I,cAAc,CAAChD;oBACvDT,qBAAqB;oBAErB,IAAIyD,gBAAgB;wBAClB;oBACF;oBACA,IAAI,CAACA,kBAAkB,CAAC9I,iBAAiB+I,eAAe,CAACjD,WAAW;wBAClE;oBACF;oBACA,kEAAkE;oBAClE,IAAIY,IAAAA,kCAAgB,EAAC0B,UAAUrC,QAAQ,CAAC,OAAO;wBAC7C;oBACF;oBAEA,MAAMiD,mBAAmBZ;oBACzBA,WAAWa,IAAAA,0BAAgB,EAACb,UAAUI,OAAO,CAAC,QAAQ;oBACtD,IAAI,CAAC3D,QAAQ,CAACuD,SAAS,EAAE;wBACvBvD,QAAQ,CAACuD,SAAS,GAAG,EAAE;oBACzB;oBACAvD,QAAQ,CAACuD,SAAS,CAAC3E,IAAI,CAACuF;oBAExB,IAAI1J,2BAA2B;wBAC7BgG,SAAS6C,GAAG,CAACC;oBACf;oBAEA,IAAI1D,YAAYqB,QAAQ,CAACqC,WAAW;wBAClC;oBACF;gBACF,OAAO;oBACL,IAAI9I,2BAA2B;wBAC7BiG,UAAU4C,GAAG,CAACC;wBACd,8DAA8D;wBAC9D,8DAA8D;wBAC9DxK,KAAKsD,SAAS,CAACgI,cAAc,CAACf,GAAG,CAACC;oBACpC;gBACF;;gBACE3B,CAAAA,YAAYzB,mBAAmBC,kBAAiB,EAAGoB,GAAG,CACtD+B,UACAtC;gBAGF,IAAIzH,UAAUyG,YAAYqE,GAAG,CAACf,WAAW;oBACvCrD,wBAAwBoD,GAAG,CAACC;gBAC9B,OAAO;oBACLtD,YAAYqD,GAAG,CAACC;gBAClB;gBAEA;;;SAGC,GACD,IAAI,sBAAsBgB,IAAI,CAAChB,WAAW;oBACxC/E,iBAAiBI,IAAI,CAAC2E;oBACtB;gBACF;gBAEA1D,YAAYjB,IAAI,CAAC2E;YACnB;YAEA,MAAMiB,iBAAiBtE,wBAAwBuE,IAAI;YACnDlE,wBAAwBiE,iBAAiB/E,6BAA6BgF,IAAI;YAE1E,IAAIlE,0BAA0B,GAAG;gBAC/B,IAAIiE,iBAAiB,GAAG;oBACtB,IAAIE,eAAe,CAAC,6BAA6B,EAC/CF,mBAAmB,IAAI,SAAS,SACjC,0DAA0D,CAAC;oBAE5D,KAAK,MAAMG,KAAKzE,wBAAyB;wBACvC,MAAM0E,UAAUlK,aAAI,CAACmK,QAAQ,CAAC1L,KAAKgH,iBAAiBiB,GAAG,CAACuD;wBACxD,MAAMG,YAAYpK,aAAI,CAACmK,QAAQ,CAAC1L,KAAKiH,mBAAmBgB,GAAG,CAACuD;wBAC5DD,gBAAgB,CAAC,GAAG,EAAEI,UAAU,KAAK,EAAEF,QAAQ,GAAG,CAAC;oBACrD;oBACAlJ,YAAYqJ,iBAAiB,CAAC,qBAAuB,CAAvB,IAAIC,MAAMN,eAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsB;gBACtD,OAAO,IAAIF,mBAAmB,GAAG;oBAC/B9I,YAAYuJ,mBAAmB;oBAC/B,MAAMrM,qBAAqBG,MAAM,kBAAkByE;gBACrD;YACF;YAEAiC,+BAA+BS;YAE/B,IAAIgF;YACJ,IAAI7L,WAAWqD,YAAY,CAACyI,kBAAkB,EAAE;gBAC9CD,sBAAsBE,IAAAA,kDAAwB,EAC5CC,OAAOvE,IAAI,CAACd,WACZ3G,WAAWqD,YAAY,CAAC4I,2BAA2B,GAC/C,AAAC,CAAA,AAACjM,WAAmBkM,kBAAkB,IAAI,EAAE,AAAD,EAAG9L,MAAM,CACnD,CAAC+L,IAAW,CAACA,EAAEC,QAAQ,IAEzB,EAAE,EACNpM,WAAWqD,YAAY,CAACgJ,6BAA6B;gBAGvD,IACE,CAAClG,+BACDmG,KAAKC,SAAS,CAACpG,iCACbmG,KAAKC,SAAS,CAACV,sBACjB;oBACA7E,YAAY;oBACZb,8BAA8B0F;gBAChC;YACF;YAEA,IAAI,CAAClM,mBAAmBuG,mBAAmB;gBACzC,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAMzG,iBAAiBC,MACpB8M,IAAI,CAAC;oBACJvF,iBAAiB;gBACnB,GACCwF,KAAK,CAAC,KAAO;YAClB;YAEA,IAAIzF,aAAaC,gBAAgB;oBAiE/B5E;gBAhEA,IAAI2E,WAAW;wBAWUhH;oBAVvB,MAAM,EAAE0M,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EACtC7M,KACA8M,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBtD,MACA,MACA,CAACuD;wBACCvD,KAAIwD,IAAI,CAAC,CAAC,YAAY,EAAED,aAAa;oBACvC;oBAGF,IAAIpN,qBAAmBK,2BAAAA,WAAWqD,YAAY,qBAAvBrD,yBAAyBiN,QAAQ,GAAE;wBACxD,0DAA0D;wBAC1DC,IAAAA,0CAAoB,EAAC;4BACnBnN;4BACA2M,gBAAgB;mCACXA;gCACH;oCACErL,MAAMrB,WAAWmN,cAAc;oCAC/BN,KAAK7M,WAAW6M,GAAG;oCACnBO,UAAU;gCACZ;6BACD;wBACH;oBACF;oBAEA,MAAM7N,qBAAqBG,MAAM,iBAAiB;wBAChD;4BAAE2N,KAAK;4BAAMC,aAAa;4BAAMC,QAAQ;wBAAK;qBAC9C;gBACH;gBACA,IAAIC;gBAIJ,IAAIvG,gBAAgB;oBAClB,IAAI;wBACFuG,iBAAiB,MAAMC,IAAAA,qBAAY,EAAC3N,KAAKE;oBAC3C,EAAE,OAAO0E,GAAG;oBACV,2EAA2E,GAC7E;gBACF;gBAEA,IAAIrC,YAAYqL,gBAAgB,EAAE;oBAChC,MAAMC,cACJjO,KAAKsD,SAAS,CAACD,QAAQ,CAAC6K,UAAU,CAAChJ,MAAM,GAAG,KAC5ClF,KAAKsD,SAAS,CAACD,QAAQ,CAAC8K,WAAW,CAACjJ,MAAM,GAAG,KAC7ClF,KAAKsD,SAAS,CAACD,QAAQ,CAAC+K,QAAQ,CAAClJ,MAAM,GAAG;oBAE5C,MAAMvC,YAAYqL,gBAAgB,CAACK,MAAM,CAAC;wBACxCC,WAAWC,IAAAA,oBAAe,EAAC;4BACzBC,aAAa;4BACbrC;4BACApJ,QAAQzC;4BACRqN,KAAK;4BACLtN;4BACAoO,qBACEzO,KAAKM,UAAU,CAACqD,YAAY,CAAC8K,mBAAmB;4BAClDR;4BACA,kBAAkB;4BAClBpH,oBAAoBpC;wBACtB;oBACF;gBACF;iBAEA9B,oCAAAA,YAAY+L,oBAAoB,qBAAhC/L,kCAAkCgM,OAAO,CAAC,CAAC5L,QAAQ6L;oBACjD,MAAMC,WAAWD,QAAQ;oBACzB,MAAME,eAAeF,QAAQ;oBAC7B,MAAMG,eAAeH,QAAQ;oBAC7B,MAAMX,cACJjO,KAAKsD,SAAS,CAACD,QAAQ,CAAC6K,UAAU,CAAChJ,MAAM,GAAG,KAC5ClF,KAAKsD,SAAS,CAACD,QAAQ,CAAC8K,WAAW,CAACjJ,MAAM,GAAG,KAC7ClF,KAAKsD,SAAS,CAACD,QAAQ,CAAC+K,QAAQ,CAAClJ,MAAM,GAAG;oBAE5C,IAAIqC,gBAAgB;4BAClBxE,yBAAAA;yBAAAA,kBAAAA,OAAO8B,OAAO,sBAAd9B,0BAAAA,gBAAgBiM,OAAO,qBAAvBjM,wBAAyB4L,OAAO,CAAC,CAACM;4BAChC,mDAAmD;4BACnD,kCAAkC;4BAClC,IAAIA,kBAAkBC,wCAAmB,IAAIpB,gBAAgB;oCAGlC/K,yBAAAA,iBAqBrBoM;gCAvBJ,MAAM,EAAEC,eAAe,EAAED,QAAQ,EAAE,GAAGrB;gCACtC,MAAMuB,yBAAyBJ,OAAOG,eAAe;gCACrD,MAAME,oBAAmBvM,kBAAAA,OAAO8B,OAAO,sBAAd9B,0BAAAA,gBAAgBwM,OAAO,qBAAvBxM,wBAAyByM,SAAS,CACzD,CAACvL,OAASA,UAASoL,0CAAAA,uBAAwBI,OAAO;gCAGpD,IAAIL,iBAAiB;oCACnB,IACEA,gBAAgBK,OAAO,MAAKJ,0CAAAA,uBAAwBI,OAAO,GAC3D;wCACA,qCAAqC;wCACrC,IAAIH,oBAAoBA,mBAAmB,CAAC,GAAG;gDAC7CvM,0BAAAA;6CAAAA,mBAAAA,OAAO8B,OAAO,sBAAd9B,2BAAAA,iBAAgBwM,OAAO,qBAAvBxM,yBAAyB2M,MAAM,CAACJ,kBAAkB;wCACpD;wCAEA,wEAAwE;wCACxE,mEAAmE;wCACnE,IAAI,CAACF,gBAAgBO,UAAU,EAAE;gDAC/B5M,0BAAAA;6CAAAA,mBAAAA,OAAO8B,OAAO,sBAAd9B,2BAAAA,iBAAgBwM,OAAO,qBAAvBxM,yBAAyB8C,IAAI,CAACuJ,gBAAgBK,OAAO;wCACvD;oCACF;gCACF;gCAEA,IAAIN,CAAAA,6BAAAA,4BAAAA,SAAUS,eAAe,qBAAzBT,0BAA2BU,KAAK,KAAIT,iBAAiB;oCACvD9C,OAAOvE,IAAI,CAACkH,OAAOY,KAAK,EAAElB,OAAO,CAAC,CAACmB;wCACjC,OAAOb,OAAOY,KAAK,CAACC,IAAI;oCAC1B;oCACAxD,OAAOyD,MAAM,CAACd,OAAOY,KAAK,EAAEV,SAASS,eAAe,CAACC,KAAK;oCAC1DZ,OAAOG,eAAe,GAAGA;gCAC3B;4BACF;wBACF;oBACF;oBAEA,IAAI9H,WAAW;4BACbvE;yBAAAA,kBAAAA,OAAOiM,OAAO,qBAAdjM,gBAAgB4L,OAAO,CAAC,CAACM;4BACvB,qDAAqD;4BACrD,sCAAsC;4BACtC,IACEA,UACA,OAAOA,OAAOe,WAAW,KAAK,YAC9Bf,OAAOe,WAAW,CAACC,iBAAiB,EACpC;gCACA,MAAMC,YAAYC,IAAAA,6BAAY,EAAC;oCAC7B3B,aAAa;oCACbrC;oCACApJ,QAAQzC;oCACRqN,KAAK;oCACLtN;oCACAoO,qBACEzO,KAAKM,UAAU,CAACqD,YAAY,CAAC8K,mBAAmB;oCAClDR;oCACAY;oCACAE;oCACAqB,yBAAyBtB,gBAAgBC;oCACzCD;oCACAjI,oBAAoBpC;gCACtB;gCAEA6H,OAAOvE,IAAI,CAACkH,OAAOe,WAAW,EAAErB,OAAO,CAAC,CAACmB;oCACvC,IAAI,CAAEA,CAAAA,OAAOI,SAAQ,GAAI;wCACvB,OAAOjB,OAAOe,WAAW,CAACF,IAAI;oCAChC;gCACF;gCACAxD,OAAOyD,MAAM,CAACd,OAAOe,WAAW,EAAEE;4BACpC;wBACF;oBACF;gBACF;gBACA,MAAMvN,YAAY0N,UAAU,CAAC;oBAC3BC,yBAAyBhJ;gBAC3B;YACF;YAEA,IAAI7B,iBAAiBP,MAAM,GAAG,GAAG;gBAC/B4E,KAAIC,KAAK,CACP,qBAIC,CAJD,IAAIwG,6BAAqB,CACvB9K,kBACArF,KACCI,YAAYC,SAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIA,GAAE+P,OAAO;gBAEX/K,mBAAmB,EAAE;YACvB;YAEA,sEAAsE;YACtElD,aAAakO,aAAa,GAAGnE,OAAOoE,WAAW,CAC7CpE,OAAOqE,OAAO,CAAC1J,UAAUtB,GAAG,CAAC,CAAC,CAACiL,GAAGC,EAAE,GAAK;oBAACD;oBAAGC,EAAE7I,IAAI;iBAAG;YAExD,MAAMnI,qBACJG,MACA,iBACAuC,aAAakO,aAAa;YAG5B,gDAAgD;YAChDlO,aAAa0H,UAAU,GAAGpD,qBACtB;gBACEiK,OAAO;gBACPzM,MAAM;gBACN6F,UAAUrD;YACZ,IACApC;YAEJ,MAAM5E,qBAAqBG,MAAM,cAAcuC,aAAa0H,UAAU;YACtE1H,aAAawO,cAAc,GAAGtJ;YAE9BzH,KAAKsD,SAAS,CAAC0N,iBAAiB,GAAGzO,EAAAA,2BAAAA,aAAa0H,UAAU,qBAAvB1H,yBAAyB2H,QAAQ,IAChE+G,IAAAA,iDAAyB,GAAC1O,4BAAAA,aAAa0H,UAAU,qBAAvB1H,0BAAyB2H,QAAQ,IAC3DzF;YAEJ,MAAMyM,qBAAqBC,IAAAA,sEAAkC,EAC3D7E,OAAOvE,IAAI,CAACd,WACZjH,KAAKM,UAAU,CAAC8Q,QAAQ,EACxBzL,GAAG,CAAC,CAAC1B,OACLoN,IAAAA,4BAAgB,EACd,wBACApN,MACAjE,KAAKM,UAAU,CAAC8Q,QAAQ,EACxBpR,KAAKM,UAAU,CAACqD,YAAY,CAAC2N,mBAAmB;YAIpDtR,KAAKsD,SAAS,CAACD,QAAQ,CAAC8K,WAAW,CAACtI,IAAI,IAAIqL;YAE5C,MAAMK,gBACJ,AAAC,OAAOjR,WAAWiR,aAAa,KAAK,cAClC,OAAMjR,WAAWiR,aAAa,oBAAxBjR,WAAWiR,aAAa,MAAxBjR,YACL,CAAC,GACD;gBACEqN,KAAK;gBACLvN,KAAKJ,KAAKI,GAAG;gBACboR,QAAQ;gBACRnR,SAASA;gBACT2C,SAAS;YACX,OAEJ,CAAC;YAEH,MAAMyO,uBAAuBnF,OAAOqE,OAAO,CAACY,iBAAiB,CAAC;YAE9D,IAAIE,qBAAqBvM,MAAM,GAAG,GAAG;gBACnClF,KAAKsD,SAAS,CAACoO,mBAAmB,GAAGD,qBAAqB9L,GAAG,CAC3D,CAAC,CAACmK,KAAK6B,MAAM,GACXN,IAAAA,4BAAgB,EACd,wBACA;wBACEO,QAAQ9B;wBACR+B,aAAa,GAAGF,MAAMtN,IAAI,GACxBsN,MAAMG,KAAK,GAAG,MAAM,KACnBC,oBAAE,CAAClF,SAAS,CAAC8E,MAAMG,KAAK,GAAG;oBAChC,GACA9R,KAAKM,UAAU,CAAC8Q,QAAQ,EACxBpR,KAAKM,UAAU,CAACqD,YAAY,CAAC2N,mBAAmB;YAGxD;YAEA,IAAI;gBACF,gEAAgE;gBAChE,qEAAqE;gBACrE,kEAAkE;gBAClE,MAAMU,eAAeC,IAAAA,sBAAe,EAACnL;gBAErC9G,KAAKsD,SAAS,CAAC4O,aAAa,GAAGF,aAAarM,GAAG,CAC7C,CAACtB;oBACC,MAAM8N,QAAQC,IAAAA,yBAAa,EAAC/N;oBAC5B,OAAO;wBACL8N,OAAOA,MAAME,EAAE,CAACC,QAAQ;wBACxBxB,OAAOyB,IAAAA,6BAAe,EAACJ;wBACvB9N;oBACF;gBACF;gBAGF,MAAMmO,aAAkD,EAAE;gBAE1D,KAAK,MAAMnO,QAAQ2N,aAAc;oBAC/B,MAAMS,QAAQC,IAAAA,8BAAc,EAACrO,MAAM;oBACnC,MAAMsO,aAAaP,IAAAA,yBAAa,EAACK,MAAMpO,IAAI;oBAC3CmO,WAAW3M,IAAI,CAAC;wBACd,GAAG4M,KAAK;wBACRN,OAAOQ,WAAWN,EAAE,CAACC,QAAQ;wBAC7BxB,OAAOyB,IAAAA,6BAAe,EAAC;4BACrB,+DAA+D;4BAC/D,uCAAuC;4BACvCF,IAAIrS,KAAKM,UAAU,CAACsS,IAAI,GACpB,IAAIC,OACFJ,MAAMK,cAAc,CAAClI,OAAO,CAC1B,CAAC,aAAa,CAAC,EACf,CAAC,mCAAmC,CAAC,KAGzC,IAAIiI,OAAOJ,MAAMK,cAAc;4BACnCC,QAAQJ,WAAWI,MAAM;wBAC3B;oBACF;gBACF;gBACA/S,KAAKsD,SAAS,CAAC4O,aAAa,CAACc,OAAO,IAAIR;gBAExC,IAAI,EAAC7N,oCAAAA,iBAAkBsO,KAAK,CAAC,CAACC,KAAKtE,MAAQsE,QAAQlB,YAAY,CAACpD,IAAI,IAAG;oBACrE,MAAMuE,cAAcnB,aAAatR,MAAM,CACrC,CAAC+R,QAAU,CAAC9N,iBAAiBwD,QAAQ,CAACsK;oBAExC,MAAMW,gBAAgBzO,iBAAiBjE,MAAM,CAC3C,CAAC+R,QAAU,CAACT,aAAa7J,QAAQ,CAACsK;oBAGpC,8CAA8C;oBAC9C9P,YAAY0Q,IAAI,CAAC;wBACfC,QAAQC,6CAA2B,CAACC,yBAAyB;wBAC7DC,MAAM;4BACJ;gCACEC,kBAAkB;4BACpB;yBACD;oBACH;oBAEAP,YAAYxE,OAAO,CAAC,CAAC8D;wBACnB9P,YAAY0Q,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACI,UAAU;4BAC9CF,MAAM;gCAAChB;6BAAM;wBACf;oBACF;oBAEAW,cAAczE,OAAO,CAAC,CAAC8D;wBACrB9P,YAAY0Q,IAAI,CAAC;4BACfC,QAAQC,6CAA2B,CAACK,YAAY;4BAChDH,MAAM;gCAAChB;6BAAM;wBACf;oBACF;gBACF;gBACA9N,mBAAmBqN;gBAEnB,IAAI,CAACtN,UAAU;oBACbG;oBACAH,WAAW;gBACb;YACF,EAAE,OAAOmP,GAAG;gBACV,IAAI,CAACnP,UAAU;oBACbI,OAAO+O;oBACPnP,WAAW;gBACb,OAAO;oBACLoF,KAAIgK,IAAI,CAAC,oCAAoCD;gBAC/C;YACF,SAAU;gBACR,kEAAkE;gBAClE,4DAA4D;gBAC5D,MAAMhU,qBAAqBG,MAAM,kBAAkByE;YACrD;QACF;QAEAsB,GAAGgO,KAAK,CAAC;YAAE1O,aAAa;gBAACjF;aAAI;YAAE4T,WAAW;QAAE;IAC9C;IAEA,MAAMC,0BAA0B,CAAC,OAAO,EAAEnQ,mCAAwB,CAAC,aAAa,EAAEoQ,oCAAyB,EAAE;IAC7GlU,KAAKsD,SAAS,CAAC6Q,iBAAiB,CAAC5J,GAAG,CAAC0J;IAErC,MAAMG,4BAA4B,CAAC,OAAO,EAAEtQ,mCAAwB,CAAC,aAAa,EAAEuQ,yCAA8B,EAAE;IACpHrU,KAAKsD,SAAS,CAAC6Q,iBAAiB,CAAC5J,GAAG,CAAC6J;IAErC,MAAME,qCAAqC,CAAC,OAAO,EAAExQ,mCAAwB,CAAC,aAAa,EAAEyQ,+CAAoC,EAAE;IACnIvU,KAAKsD,SAAS,CAAC6Q,iBAAiB,CAAC5J,GAAG,CAAC+J;IAErC,eAAeE,eAAeC,GAAoB,EAAEC,GAAmB;YAGjEC,qBAcFA,sBACAA;QAjBF,MAAMA,YAAYC,YAAG,CAACC,KAAK,CAACJ,IAAIG,GAAG,IAAI;QAEvC,KAAID,sBAAAA,UAAUzO,QAAQ,qBAAlByO,oBAAoBxM,QAAQ,CAAC8L,0BAA0B;YACzDS,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgB;YAC9BL,IAAIM,GAAG,CACLpI,KAAKC,SAAS,CAAC;gBACb1H,OAAOR,iBAAiBjE,MAAM,CAC5B,CAAC+R,QAAU,CAACzS,KAAKsD,SAAS,CAACoE,QAAQ,CAAC6D,GAAG,CAACkH;YAE5C;YAEF,OAAO;gBAAEwC,UAAU;YAAK;QAC1B;QAEA,IACEN,EAAAA,uBAAAA,UAAUzO,QAAQ,qBAAlByO,qBAAoBxM,QAAQ,CAACiM,iCAC7BO,uBAAAA,UAAUzO,QAAQ,qBAAlByO,qBAAoBxM,QAAQ,CAACmM,sCAC7B;gBAGuB/R;YAFvBmS,IAAII,UAAU,GAAG;YACjBJ,IAAIK,SAAS,CAAC,gBAAgB;YAC9BL,IAAIM,GAAG,CAACpI,KAAKC,SAAS,CAACtK,EAAAA,2BAAAA,aAAa0H,UAAU,qBAAvB1H,yBAAyB2H,QAAQ,KAAI,EAAE;YAC9D,OAAO;gBAAE+K,UAAU;YAAK;QAC1B;QACA,OAAO;YAAEA,UAAU;QAAM;IAC3B;IAEA,SAASC,0BACPC,GAAY,EACZjR,IAAyE;QAEzE,IAAIiR,eAAeC,wBAAgB,EAAE;YACnC,wDAAwD;YACxDtL,KAAIC,KAAK,CAACoL,IAAI3E,OAAO;QACvB,OAAO,IAAI2E,eAAeE,8BAAsB,EAAE;QAChD,yEAAyE;QACzE,mEAAmE;QACrE,OAAO,IAAInR,SAAS,WAAW;YAC7B4F,KAAIgK,IAAI,CAACqB;QACX,OAAO,IAAIjR,SAAS,WAAW;YAC7B4F,KAAIC,KAAK,CAACoL;QACZ,OAAO,IAAIjR,MAAM;YACf4F,KAAIC,KAAK,CAAC,GAAG7F,KAAK,CAAC,CAAC,EAAEiR;QACxB,OAAO;YACLrL,KAAIC,KAAK,CAACoL;QACZ;IACF;IAEA,OAAO;QACL5S;QACAI;QACA6R;QACAU;QAEA,MAAMI,kBAAiBC,UAAmB;YACxC,IAAI,CAAChT,aAAayH,oBAAoB,EAAE;YACxC,OAAOrH,YAAYwB,UAAU,CAAC;gBAC5BE,MAAM9B,aAAayH,oBAAoB;gBACvC5F,YAAY;gBACZI,YAAYC;gBACZmQ,KAAKW;YACP;QACF;IACF;AACF;AAEO,eAAezV,gBAAgBE,IAAe;IACnD,MAAMwV,WAAW7T,aAAI,CAClBmK,QAAQ,CAAC9L,KAAKI,GAAG,EAAEJ,KAAKQ,QAAQ,IAAIR,KAAKS,MAAM,IAAI,IACnD2F,UAAU,CAAC;IAEd,MAAMqP,SAAS,MAAMjU,aAAaxB;IAElCA,KAAKoD,SAAS,CAACsS,MAAM,CACnBC,IAAAA,uBAAe,EACbhU,aAAI,CAACC,IAAI,CAAC5B,KAAKI,GAAG,EAAEJ,KAAKM,UAAU,CAACD,OAAO,GAC3CL,KAAKM,UAAU,EACf;QACEsV,gBAAgB;QAChBJ;QACAK,WAAW,CAAC,CAAC7V,KAAK4C,KAAK;QACvBkT,YAAY;QACZrV,QAAQ,CAAC,CAACT,KAAKS,MAAM;QACrBD,UAAU,CAAC,CAACR,KAAKQ,QAAQ;QACzBuV,gBAAgB,CAAC,CAAC/V,KAAK+V,cAAc;QACrCC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;YAAEC,KAAKlW,KAAKI,GAAG;QAAC;IAC1D;IAIJ,4CAA4C;IAC5CJ,KAAKoD,SAAS,CAACsS,MAAM,CAAC;QACpBS,WAAWC,iCAAyB;QACpCC,SAAS;YACPC,aAAa;YACbC,iBAAiBC,IAAAA,kCAA0B,EAACxW,KAAKM,UAAU,IAAI,IAAI;QACrE;IACF;IAEA,OAAOmV;AACT;CAIA,2DAA2D"}